/* Gesco Stay User Manual - Main Styles */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #8B4513;
    --primary-dark: #5D2F0A;
    --primary-light: #A0522D;
    --secondary-color: #D2691E;
    --accent-color: #CD853F;
    --text-primary: #2C3E50;
    --text-secondary: #5D6D7E;
    --text-light: #85929E;
    --background-primary: #FFFFFF;
    --background-secondary: #F8F9FA;
    --background-accent: #FDF6F0;
    --border-color: #E5E8EC;
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-secondary);
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

h1 { font-size: 2.5rem; font-weight: 700; }
h2 { font-size: 2rem; color: var(--primary-color); }
h3 { font-size: 1.5rem; color: var(--primary-light); }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    text-align: justify;
}

/* Page Structure */
.page {
    min-height: 100vh;
    padding: 2rem;
    background: var(--background-primary);
    margin-bottom: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    page-break-after: always;
    position: relative;
}

/* Cover Page */
.cover-page {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cover-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.cover-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
}

.logo-section {
    margin-bottom: 3rem;
}

.logo-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 2rem;
}

.logo-placeholder i {
    font-size: 3rem;
    color: var(--accent-color);
}

.title-section h1 {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.title-section h2 {
    font-size: 2.5rem;
    font-weight: 400;
    color: var(--accent-color);
    margin-bottom: 2rem;
}

.subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto 3rem;
}

.cover-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 3rem;
}

.detail-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.detail-item i {
    color: var(--accent-color);
    width: 20px;
}

.cover-footer {
    opacity: 0.8;
    font-size: 0.9rem;
}

/* Table of Contents */
.toc-page {
    background: var(--background-primary);
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 3px solid var(--primary-color);
}

.page-header h1 {
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.toc-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.toc-section h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-color);
}

.toc-items {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toc-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--background-secondary);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition);
    border-left: 4px solid transparent;
}

.toc-item:hover {
    background: var(--background-accent);
    border-left-color: var(--primary-color);
    transform: translateX(5px);
}

.toc-number {
    background: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 1rem;
    flex-shrink: 0;
}

.toc-title {
    flex: 1;
    font-weight: 500;
}

.toc-page {
    color: var(--text-light);
    font-size: 0.9rem;
}

.quick-reference {
    background: var(--background-accent);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 2px solid var(--accent-color);
}

.quick-reference h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.quick-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: white;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.quick-item i {
    color: var(--primary-color);
    width: 20px;
}

/* Navigation */
.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: var(--background-primary);
    border-right: 1px solid var(--border-color);
    z-index: 1000;
    overflow-y: auto;
    transform: translateX(-100%);
    transition: var(--transition);
    box-shadow: var(--shadow-medium);
}

.main-nav.active {
    transform: translateX(0);
}

.nav-brand {
    padding: 1.5rem;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.nav-sections {
    padding: 1rem 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.nav-item:hover,
.nav-item.active {
    background: var(--background-accent);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.nav-number {
    background: var(--primary-light);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

.nav-toggle {
    position: fixed;
    top: 1rem;
    left: 1rem;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1001;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
}

.nav-toggle:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

/* Progress Bar */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(139, 69, 19, 0.1);
    z-index: 999;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    width: 0%;
    transition: width 0.3s ease;
}

/* Content Container */
.content-container {
    margin-left: 0;
    transition: var(--transition);
    min-height: 100vh;
}

.content-container.nav-open {
    margin-left: 280px;
}

/* Section Styles */
.section {
    background: var(--background-primary);
    margin: 2rem;
    padding: 3rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    page-break-after: always;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 3px solid var(--primary-color);
}

.section-number {
    background: var(--primary-color);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1rem;
}

.section-title {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Content Elements */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.content-card {
    background: var(--background-secondary);
    padding: 2rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
}

.content-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.card-icon {
    color: var(--primary-color);
    font-size: 2rem;
    margin-bottom: 1rem;
}

.card-title {
    color: var(--primary-color);
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

/* Lists */
ul, ol {
    margin: 1rem 0;
    padding-left: 2rem;
}

li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

/* Step Lists */
.step-list {
    counter-reset: step-counter;
    list-style: none;
    padding: 0;
}

.step-item {
    counter-increment: step-counter;
    background: var(--background-secondary);
    margin: 1rem 0;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    position: relative;
    padding-left: 4rem;
}

.step-item::before {
    content: counter(step-counter);
    position: absolute;
    left: 1.5rem;
    top: 1.5rem;
    background: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Image Placeholders */
.image-placeholder {
    background: var(--background-secondary);
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 3rem;
    text-align: center;
    margin: 2rem 0;
    color: var(--text-light);
    transition: var(--transition);
}

.image-placeholder:hover {
    border-color: var(--primary-color);
    background: var(--background-accent);
}

.image-placeholder i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: block;
}

.image-placeholder .placeholder-text {
    font-weight: 500;
    color: var(--text-secondary);
}

.image-placeholder .placeholder-description {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-top: 0.5rem;
}

/* Callout Boxes */
.callout {
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin: 2rem 0;
    border-left: 4px solid;
}

.callout-info {
    background: #E3F2FD;
    border-color: #2196F3;
    color: #1565C0;
}

.callout-warning {
    background: #FFF3E0;
    border-color: #FF9800;
    color: #E65100;
}

.callout-success {
    background: #E8F5E8;
    border-color: #4CAF50;
    color: #2E7D32;
}

.callout-danger {
    background: #FFEBEE;
    border-color: #F44336;
    color: #C62828;
}

.callout-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 2rem 0;
    background: var(--background-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

tr:hover {
    background: var(--background-secondary);
}

/* Footer */
.main-footer {
    background: var(--text-primary);
    color: white;
    padding: 3rem 2rem 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-section h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.footer-section p {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-bottom {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-nav {
        width: 100%;
    }
    
    .content-container.nav-open {
        margin-left: 0;
    }
    
    .page {
        padding: 1rem;
        margin: 1rem;
    }
    
    .section {
        margin: 1rem;
        padding: 2rem;
    }
    
    .title-section h1 {
        font-size: 2.5rem;
    }
    
    .title-section h2 {
        font-size: 1.8rem;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .toc-content {
        grid-template-columns: 1fr;
    }
    
    .quick-items {
        grid-template-columns: 1fr;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
