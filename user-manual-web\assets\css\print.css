/* Print Styles for PDF Conversion */

@media print {
    /* Reset for print */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* Page setup */
    @page {
        size: A4;
        margin: 2cm 1.5cm;
        
        @top-center {
            content: "Gesco Stay User Manual";
            font-family: 'Inter', sans-serif;
            font-size: 10pt;
            color: #8B4513;
            border-bottom: 1px solid #8B4513;
            padding-bottom: 5pt;
        }
        
        @bottom-right {
            content: "Page " counter(page);
            font-family: 'Inter', sans-serif;
            font-size: 9pt;
            color: #666;
        }
        
        @bottom-left {
            content: "© 2025 Gesco Stay";
            font-family: 'Inter', sans-serif;
            font-size: 9pt;
            color: #666;
        }
    }

    /* Special page for cover */
    @page cover {
        margin: 0;
        
        @top-center { content: none; }
        @bottom-right { content: none; }
        @bottom-left { content: none; }
    }

    /* Special page for TOC */
    @page toc {
        @top-center {
            content: "Table of Contents";
        }
    }

    /* Body and layout */
    html, body {
        font-size: 11pt;
        line-height: 1.4;
        color: #333;
        background: white !important;
    }

    /* Hide navigation and interactive elements */
    .main-nav,
    .nav-toggle,
    .progress-bar,
    .main-footer {
        display: none !important;
    }

    /* Page structure */
    .page {
        page-break-after: always;
        page-break-inside: avoid;
        margin: 0;
        padding: 0;
        background: white !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        min-height: auto;
    }

    /* Cover page */
    .cover-page {
        page: cover;
        background: linear-gradient(135deg, #8B4513 0%, #5D2F0A 100%) !important;
        color: white !important;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        min-height: 100vh;
        padding: 2cm;
    }

    .cover-page * {
        color: white !important;
    }

    .title-section h1 {
        font-size: 36pt !important;
        font-weight: 700;
        margin-bottom: 12pt;
    }

    .title-section h2 {
        font-size: 24pt !important;
        margin-bottom: 24pt;
    }

    .subtitle {
        font-size: 14pt !important;
        margin-bottom: 36pt;
    }

    .detail-item {
        font-size: 12pt !important;
        margin-bottom: 8pt;
    }

    /* Table of Contents */
    .toc-page {
        page: toc;
        padding: 1cm;
    }

    .toc-content {
        display: block;
    }

    .toc-section {
        margin-bottom: 24pt;
        break-inside: avoid;
    }

    .toc-section h2 {
        font-size: 14pt;
        color: #8B4513 !important;
        border-bottom: 1pt solid #8B4513;
        padding-bottom: 6pt;
        margin-bottom: 12pt;
    }

    .toc-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6pt 0;
        border-bottom: 1pt dotted #ccc;
        text-decoration: none;
        color: #333 !important;
        break-inside: avoid;
    }

    .toc-number {
        background: #8B4513 !important;
        color: white !important;
        width: 24pt;
        height: 24pt;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 10pt;
        font-weight: 600;
        margin-right: 12pt;
    }

    .toc-title {
        flex: 1;
        font-weight: 500;
    }

    .toc-page-num {
        font-size: 10pt;
        color: #666;
    }

    /* Section pages */
    .section {
        padding: 1cm;
        margin: 0;
        background: white !important;
        box-shadow: none !important;
        border-radius: 0 !important;
    }

    .section-header {
        text-align: center;
        margin-bottom: 24pt;
        padding-bottom: 12pt;
        border-bottom: 2pt solid #8B4513;
        break-after: avoid;
    }

    .section-number {
        background: #8B4513 !important;
        color: white !important;
        width: 48pt;
        height: 48pt;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 18pt;
        font-weight: 700;
        margin-bottom: 12pt;
    }

    .section-title {
        font-size: 24pt;
        color: #8B4513 !important;
        margin-bottom: 6pt;
    }

    .section-subtitle {
        font-size: 12pt;
        color: #666 !important;
    }

    /* Typography */
    h1 {
        font-size: 20pt;
        color: #8B4513 !important;
        margin-bottom: 12pt;
        break-after: avoid;
    }

    h2 {
        font-size: 16pt;
        color: #8B4513 !important;
        margin-top: 18pt;
        margin-bottom: 10pt;
        break-after: avoid;
    }

    h3 {
        font-size: 14pt;
        color: #A0522D !important;
        margin-top: 14pt;
        margin-bottom: 8pt;
        break-after: avoid;
    }

    h4 {
        font-size: 12pt;
        color: #5D4037 !important;
        margin-top: 12pt;
        margin-bottom: 6pt;
        break-after: avoid;
    }

    h5, h6 {
        font-size: 11pt;
        color: #333 !important;
        margin-top: 10pt;
        margin-bottom: 6pt;
        break-after: avoid;
    }

    p {
        margin-bottom: 8pt;
        text-align: justify;
        color: #333 !important;
        orphans: 3;
        widows: 3;
    }

    /* Lists */
    ul, ol {
        margin: 8pt 0;
        padding-left: 18pt;
    }

    li {
        margin-bottom: 4pt;
        color: #333 !important;
        break-inside: avoid;
    }

    /* Step lists */
    .step-list {
        list-style: none;
        padding: 0;
        counter-reset: step-counter;
    }

    .step-item {
        counter-increment: step-counter;
        background: #f8f9fa !important;
        margin: 8pt 0;
        padding: 12pt;
        border-left: 3pt solid #8B4513;
        position: relative;
        padding-left: 36pt;
        break-inside: avoid;
    }

    .step-item::before {
        content: counter(step-counter);
        position: absolute;
        left: 12pt;
        top: 12pt;
        background: #8B4513 !important;
        color: white !important;
        width: 18pt;
        height: 18pt;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 9pt;
        font-weight: 600;
    }

    /* Content cards */
    .content-card {
        background: #f8f9fa !important;
        padding: 12pt;
        border-left: 3pt solid #8B4513;
        margin: 8pt 0;
        break-inside: avoid;
    }

    .card-title {
        color: #8B4513 !important;
        font-size: 12pt;
        font-weight: 600;
        margin-bottom: 6pt;
    }

    /* Image placeholders */
    .image-placeholder {
        background: #f0f0f0 !important;
        border: 1pt dashed #ccc !important;
        padding: 24pt;
        text-align: center;
        margin: 12pt 0;
        break-inside: avoid;
    }

    .image-placeholder i {
        font-size: 24pt;
        color: #8B4513 !important;
        display: block;
        margin-bottom: 8pt;
    }

    .placeholder-text {
        font-weight: 500;
        color: #333 !important;
        font-size: 10pt;
    }

    .placeholder-description {
        font-size: 9pt;
        color: #666 !important;
        margin-top: 4pt;
    }

    /* Callouts */
    .callout {
        padding: 12pt;
        margin: 12pt 0;
        border-left: 3pt solid;
        break-inside: avoid;
    }

    .callout-info {
        background: #e3f2fd !important;
        border-color: #2196f3 !important;
    }

    .callout-warning {
        background: #fff3e0 !important;
        border-color: #ff9800 !important;
    }

    .callout-success {
        background: #e8f5e8 !important;
        border-color: #4caf50 !important;
    }

    .callout-danger {
        background: #ffebee !important;
        border-color: #f44336 !important;
    }

    .callout-title {
        font-weight: 600;
        margin-bottom: 4pt;
        font-size: 10pt;
    }

    /* Tables */
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 12pt 0;
        break-inside: avoid;
        font-size: 10pt;
    }

    th, td {
        padding: 6pt;
        border: 1pt solid #ccc;
        text-align: left;
    }

    th {
        background: #8B4513 !important;
        color: white !important;
        font-weight: 600;
    }

    tr {
        break-inside: avoid;
    }

    /* Links */
    a {
        color: #8B4513 !important;
        text-decoration: none;
    }

    a::after {
        content: " (" attr(href) ")";
        font-size: 9pt;
        color: #666;
    }

    /* Remove link URLs for internal navigation */
    a[href^="#"]::after {
        content: "";
    }

    /* Code */
    code {
        background: #f4f4f4 !important;
        padding: 2pt 4pt;
        font-family: 'Courier New', monospace;
        font-size: 9pt;
        color: #d63384 !important;
    }

    pre {
        background: #f8f9fa !important;
        border: 1pt solid #e9ecef;
        padding: 12pt;
        overflow: visible;
        font-size: 9pt;
        break-inside: avoid;
    }

    /* Grid layouts become single column */
    .content-grid,
    .toc-content,
    .quick-items {
        display: block !important;
    }

    .content-grid > *,
    .toc-content > *,
    .quick-items > * {
        margin-bottom: 12pt;
        break-inside: avoid;
    }

    /* Quick reference */
    .quick-reference {
        background: #fdf6f0 !important;
        padding: 12pt;
        border: 1pt solid #8B4513;
        break-inside: avoid;
    }

    .quick-reference h3 {
        color: #8B4513 !important;
        margin-bottom: 8pt;
        font-size: 12pt;
    }

    .quick-item {
        background: white !important;
        padding: 6pt;
        margin-bottom: 4pt;
        font-size: 10pt;
        break-inside: avoid;
    }

    /* Page breaks */
    .page-break {
        page-break-before: always;
    }

    .avoid-break {
        break-inside: avoid;
    }

    /* Ensure proper spacing */
    .section > *:first-child {
        margin-top: 0;
    }

    .section > *:last-child {
        margin-bottom: 0;
    }
}
