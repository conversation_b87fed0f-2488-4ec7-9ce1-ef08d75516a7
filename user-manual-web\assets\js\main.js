// Gesco Stay User Manual - Main JavaScript

class UserManual {
    constructor() {
        this.currentSection = 'cover';
        this.sections = [];
        this.isNavOpen = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSections();
        this.updateProgress();
        this.setupIntersectionObserver();
        
        // Load initial content
        this.loadSectionContent();
    }

    setupEventListeners() {
        // Navigation toggle
        const navToggle = document.getElementById('navToggle');
        const mainNav = document.getElementById('mainNav');
        
        navToggle?.addEventListener('click', () => {
            this.toggleNavigation();
        });

        // Navigation items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = item.getAttribute('data-section');
                this.navigateToSection(section);
            });
        });

        // TOC items
        document.querySelectorAll('.toc-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const href = item.getAttribute('href');
                const section = href.replace('#section-', '');
                this.navigateToSection(section);
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e);
        });

        // Window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Scroll events
        window.addEventListener('scroll', () => {
            this.updateProgress();
        });

        // Print functionality
        this.setupPrintHandlers();
    }

    toggleNavigation() {
        const mainNav = document.getElementById('mainNav');
        const contentContainer = document.getElementById('contentContainer');
        
        this.isNavOpen = !this.isNavOpen;
        
        if (this.isNavOpen) {
            mainNav.classList.add('active');
            contentContainer.classList.add('nav-open');
        } else {
            mainNav.classList.remove('active');
            contentContainer.classList.remove('nav-open');
        }
    }

    navigateToSection(sectionId) {
        this.currentSection = sectionId;
        this.updateActiveNavItem();
        this.loadSectionContent();
        this.updateProgress();
        
        // Close navigation on mobile
        if (window.innerWidth <= 768) {
            this.isNavOpen = false;
            document.getElementById('mainNav').classList.remove('active');
            document.getElementById('contentContainer').classList.remove('nav-open');
        }
        
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    updateActiveNavItem() {
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeItem = document.querySelector(`[data-section="${this.currentSection}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
    }

    loadSectionContent() {
        const contentContainer = document.getElementById('contentContainer');
        
        // Show cover and TOC pages
        if (this.currentSection === 'cover') {
            document.getElementById('cover').style.display = 'flex';
            document.getElementById('toc').style.display = 'none';
            contentContainer.innerHTML = '';
            return;
        }
        
        if (this.currentSection === 'toc') {
            document.getElementById('cover').style.display = 'none';
            document.getElementById('toc').style.display = 'block';
            contentContainer.innerHTML = '';
            return;
        }
        
        // Hide cover and TOC for content sections
        document.getElementById('cover').style.display = 'none';
        document.getElementById('toc').style.display = 'none';
        
        // Load section content
        const sectionContent = this.getSectionContent(this.currentSection);
        contentContainer.innerHTML = sectionContent;
        
        // Add fade-in animation
        contentContainer.classList.add('fade-in');
        setTimeout(() => {
            contentContainer.classList.remove('fade-in');
        }, 600);
    }

    getSectionContent(sectionId) {
        const sections = {
            '01': this.getGettingStartedContent(),
            '02': this.getNavigationContent(),
            '03': this.getPropertyListingsContent(),
            '04': this.getCarRentalsContent(),
            '05': this.getHotelBookingsContent(),
            '06': this.getBookingManagementContent(),
            '07': this.getPaymentSystemContent(),
            '08': this.getProfileManagementContent(),
            '09': this.getHostFeaturesContent(),
            '10': this.getAdminFeaturesContent(),
            '11': this.getLegalPoliciesContent(),
            '12': this.getTroubleshootingContent()
        };
        
        return sections[sectionId] || '<div class="section"><h1>Section not found</h1></div>';
    }

    getGettingStartedContent() {
        return `
            <div class="section" id="section-01">
                <div class="section-header">
                    <div class="section-number">01</div>
                    <h1 class="section-title">Getting Started</h1>
                    <p class="section-subtitle">Learn the basics of using Gesco Stay, including account creation, verification, and initial setup</p>
                </div>

                <div class="content-grid">
                    <div class="content-card">
                        <i class="fas fa-globe card-icon"></i>
                        <h3 class="card-title">Platform Overview</h3>
                        <p>Gesco Stay is Africa's most trusted travel platform connecting travelers with authentic local experiences across The Gambia and West Africa.</p>
                        <ul>
                            <li><strong>Property Rentals:</strong> Authentic local homes and accommodations</li>
                            <li><strong>Car Rentals:</strong> Reliable transportation options</li>
                            <li><strong>Hotel Bookings:</strong> Professional hotel accommodations</li>
                            <li><strong>Local Experiences:</strong> Cultural tours and authentic experiences</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <i class="fas fa-user-plus card-icon"></i>
                        <h3 class="card-title">Quick Start Guide</h3>
                        <ol class="step-list">
                            <li class="step-item">Create your account using email or phone number</li>
                            <li class="step-item">Verify your account through OTP verification</li>
                            <li class="step-item">Complete your profile with personal information</li>
                            <li class="step-item">Start browsing properties, cars, and hotels</li>
                            <li class="step-item">Make your first booking</li>
                        </ol>
                    </div>
                </div>

                <h2>Account Registration</h2>
                
                <div class="callout callout-info">
                    <div class="callout-title">
                        <i class="fas fa-info-circle"></i>
                        Registration Requirements
                    </div>
                    <p>You must be 18 years or older to create an account. You'll need a valid email address or mobile phone number for verification.</p>
                </div>

                <h3>Step 1: Access Registration</h3>
                <ol class="step-list">
                    <li class="step-item">Visit the Gesco Stay website</li>
                    <li class="step-item">Click the "Sign Up" button in the top navigation</li>
                    <li class="step-item">You'll be redirected to the registration page</li>
                </ol>

                <div class="image-placeholder">
                    <i class="fas fa-image"></i>
                    <div class="placeholder-text">Registration Page Screenshot</div>
                    <div class="placeholder-description">Screenshot showing the Sign Up button location and registration form</div>
                </div>

                <h3>Step 2: Choose Registration Method</h3>
                <p>You can register using either:</p>
                <ul>
                    <li><strong>Email Address:</strong> Use your email for account creation</li>
                    <li><strong>Mobile Phone Number:</strong> Use your phone number with country code</li>
                </ul>

                <h3>Step 3: Fill Registration Form</h3>
                <p>The registration form includes the following fields in order:</p>
                <ol>
                    <li><strong>First Name:</strong> Your given name</li>
                    <li><strong>Last Name:</strong> Your family name</li>
                    <li><strong>Mobile Number:</strong> Phone number with country code (+220 for Gambia)</li>
                    <li><strong>Email Address:</strong> Your email address</li>
                    <li><strong>Password:</strong> Secure password (minimum 8 characters)</li>
                    <li><strong>Confirm Password:</strong> Re-enter your password</li>
                </ol>

                <div class="callout callout-warning">
                    <div class="callout-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Password Security
                    </div>
                    <p>Create a strong password with at least 8 characters, including uppercase, lowercase, numbers, and special characters.</p>
                </div>

                <h2>Account Verification</h2>

                <h3>OTP Verification Process</h3>
                <p>After successful registration, you'll be redirected to the OTP verification page:</p>
                <ol class="step-list">
                    <li class="step-item"><strong>Check Your Messages:</strong> An OTP code will be sent to your registered mobile number or email</li>
                    <li class="step-item"><strong>Enter OTP Code:</strong> Input the 6-digit verification code</li>
                    <li class="step-item"><strong>Verify Account:</strong> Click "Verify" to complete the process</li>
                </ol>

                <div class="callout callout-info">
                    <div class="callout-title">
                        <i class="fas fa-clock"></i>
                        Important Notes
                    </div>
                    <ul>
                        <li>OTP codes expire after 10 minutes</li>
                        <li>You can request a new code if needed</li>
                        <li>Verification is required before accessing protected features</li>
                    </ul>
                </div>

                <h2>Login Process</h2>

                <div class="content-grid">
                    <div class="content-card">
                        <i class="fas fa-envelope card-icon"></i>
                        <h3 class="card-title">Email Login</h3>
                        <ol>
                            <li>Click "Sign In" from the homepage</li>
                            <li>Select "Email" as login method</li>
                            <li>Enter your email address and password</li>
                            <li>Click "Sign In"</li>
                        </ol>
                    </div>

                    <div class="content-card">
                        <i class="fas fa-mobile-alt card-icon"></i>
                        <h3 class="card-title">Phone Number Login</h3>
                        <ol>
                            <li>Click "Sign In" from the homepage</li>
                            <li>Select "Phone" as login method</li>
                            <li>Enter your phone number with country code</li>
                            <li>Enter your password and click "Sign In"</li>
                        </ol>
                    </div>
                </div>

                <h3>Forgot Password</h3>
                <p>If you forget your password:</p>
                <ol class="step-list">
                    <li class="step-item">Click "Forgot Password?" on the login page</li>
                    <li class="step-item">Enter your email address</li>
                    <li class="step-item">Check your email for reset instructions</li>
                    <li class="step-item">Follow the link to create a new password</li>
                </ol>

                <h2>Profile Setup</h2>

                <h3>Initial Profile Configuration</h3>
                <p>After first login, complete your profile:</p>
                <ul>
                    <li><strong>Profile Picture:</strong> Upload a clear photo of yourself</li>
                    <li><strong>Personal Information:</strong> Verify your name and contact details</li>
                    <li><strong>Preferences:</strong> Set your travel preferences and interests</li>
                    <li><strong>Verification:</strong> Complete identity verification if required</li>
                </ul>

                <h3>Verification Status</h3>
                <p>Your account may have different verification levels:</p>
                <ul>
                    <li><strong>Email Verified:</strong> ✅ Email address confirmed</li>
                    <li><strong>Phone Verified:</strong> ✅ Phone number confirmed</li>
                    <li><strong>Identity Verified:</strong> ✅ Government ID verified (for hosts)</li>
                    <li><strong>Payment Verified:</strong> ✅ Payment method added</li>
                </ul>

                <div class="callout callout-success">
                    <div class="callout-title">
                        <i class="fas fa-check-circle"></i>
                        Next Steps
                    </div>
                    <p>Once your account is set up:</p>
                    <ul>
                        <li>Explore the platform and browse available services</li>
                        <li>Complete your profile for better recommendations</li>
                        <li>Start booking properties, cars, or hotels</li>
                        <li>Consider hosting to earn income</li>
                    </ul>
                </div>
            </div>
        `;
    }

    getNavigationContent() {
        return `
            <div class="section" id="section-02">
                <div class="section-header">
                    <div class="section-number">02</div>
                    <h1 class="section-title">Navigation & Interface</h1>
                    <p class="section-subtitle">Understand the platform's user interface, navigation menu, and search functionality</p>
                </div>

                <h2>Homepage Tour</h2>
                
                <div class="content-grid">
                    <div class="content-card">
                        <i class="fas fa-home card-icon"></i>
                        <h3 class="card-title">Header Section</h3>
                        <p>The homepage header contains:</p>
                        <ul>
                            <li><strong>Gesco Stay Logo:</strong> Click to return to homepage</li>
                            <li><strong>Navigation Menu:</strong> Access to main platform sections</li>
                            <li><strong>User Account:</strong> Login/Profile access</li>
                            <li><strong>Language/Currency:</strong> Platform settings</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <i class="fas fa-star card-icon"></i>
                        <h3 class="card-title">Hero Section</h3>
                        <p>The main hero section features:</p>
                        <ul>
                            <li><strong>Welcome Message:</strong> Platform introduction</li>
                            <li><strong>Search Bar:</strong> Quick property/car search</li>
                            <li><strong>Call-to-Action Buttons:</strong> "Start Hosting" and "Explore"</li>
                            <li><strong>Featured Images:</strong> Showcase of available properties</li>
                        </ul>
                    </div>
                </div>

                <div class="image-placeholder">
                    <i class="fas fa-image"></i>
                    <div class="placeholder-text">Homepage Overview Screenshot</div>
                    <div class="placeholder-description">Screenshot of the main homepage showing header, hero section, and navigation</div>
                </div>

                <h2>Navigation Menu</h2>

                <h3>Primary Navigation Items</h3>
                <p>The main navigation includes:</p>
                <ol>
                    <li><strong>Properties:</strong> Browse residential listings</li>
                    <li><strong>Hotels:</strong> Hotel accommodations</li>
                    <li><strong>Car Rentals:</strong> Vehicle rental options</li>
                </ol>

                <h3>Secondary Navigation Items</h3>
                <p>Additional navigation options:</p>
                <ol>
                    <li><strong>Restaurants:</strong> External link to Gesco restaurant platform</li>
                    <li><strong>Night Life:</strong> External link to entertainment venues</li>
                    <li><strong>About:</strong> Information about the platform</li>
                </ol>

                <h3>User-Specific Navigation</h3>
                <p>When logged in, additional options appear:</p>
                <ul>
                    <li><strong>List Property:</strong> Create a property listing</li>
                    <li><strong>List Hotel:</strong> Create a hotel listing</li>
                    <li><strong>List Car:</strong> Create a car rental listing</li>
                    <li><strong>My Bookings:</strong> View your reservations</li>
                    <li><strong>Profile:</strong> Account management</li>
                </ul>

                <div class="callout callout-info">
                    <div class="callout-title">
                        <i class="fas fa-mobile-alt"></i>
                        Responsive Navigation
                    </div>
                    <p>On smaller screens:</p>
                    <ul>
                        <li>Navigation collapses into a hamburger menu</li>
                        <li>Items that don't fit are moved to a "More" dropdown</li>
                        <li>Text size remains consistent across all screen sizes</li>
                    </ul>
                </div>

                <h2>Search Functionality</h2>

                <h3>Property Search</h3>
                <p>The main search bar allows you to:</p>
                <ol class="step-list">
                    <li class="step-item"><strong>Enter Location:</strong> Type city, area, or specific address</li>
                    <li class="step-item"><strong>Select Dates:</strong> Check-in and check-out dates</li>
                    <li class="step-item"><strong>Choose Guests:</strong> Number of guests</li>
                    <li class="step-item"><strong>Apply Filters:</strong> Price range, amenities, property type</li>
                </ol>

                <div class="image-placeholder">
                    <i class="fas fa-search"></i>
                    <div class="placeholder-text">Property Search Interface</div>
                    <div class="placeholder-description">Screenshot of the property search form with all fields visible</div>
                </div>

                <h3>Advanced Filters</h3>
                <p>Available filter options:</p>
                <ul>
                    <li><strong>Price Range:</strong> Set minimum and maximum price</li>
                    <li><strong>Property Type:</strong> House, apartment, villa, etc.</li>
                    <li><strong>Amenities:</strong> WiFi, parking, pool, kitchen, etc.</li>
                    <li><strong>Bedrooms:</strong> Number of bedrooms required</li>
                    <li><strong>Bathrooms:</strong> Number of bathrooms required</li>
                    <li><strong>Location:</strong> Specific areas or neighborhoods</li>
                </ul>

                <h3>Car Search</h3>
                <p>For car rentals:</p>
                <ol>
                    <li><strong>Pickup Location:</strong> Where to collect the car</li>
                    <li><strong>Pickup Date & Time:</strong> When you need the car</li>
                    <li><strong>Return Date & Time:</strong> When you'll return the car</li>
                    <li><strong>Car Type:</strong> Economy, standard, luxury, SUV, etc.</li>
                </ol>

                <h2>User Interface Elements</h2>

                <div class="content-grid">
                    <div class="content-card">
                        <i class="fas fa-th-large card-icon"></i>
                        <h3 class="card-title">Cards and Listings</h3>
                        <p>Property and car listings are displayed as cards containing:</p>
                        <ul>
                            <li><strong>Main Image:</strong> Primary photo</li>
                            <li><strong>Title:</strong> Property name or car model</li>
                            <li><strong>Location:</strong> Address or area</li>
                            <li><strong>Price:</strong> Per night or per day</li>
                            <li><strong>Rating:</strong> User reviews and ratings</li>
                            <li><strong>Key Features:</strong> Specifications</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <i class="fas fa-mouse-pointer card-icon"></i>
                        <h3 class="card-title">Buttons and Actions</h3>
                        <p>Common button types:</p>
                        <ul>
                            <li><strong>Primary Buttons:</strong> Main actions (Book Now, Sign Up)</li>
                            <li><strong>Secondary Buttons:</strong> Alternative actions (View Details, Save)</li>
                            <li><strong>Icon Buttons:</strong> Quick actions (Share, Favorite, Filter)</li>
                        </ul>
                    </div>
                </div>

                <h2>Mobile Experience</h2>

                <h3>Responsive Design</h3>
                <p>The platform adapts to different screen sizes:</p>
                <ul>
                    <li><strong>Desktop:</strong> Full navigation and sidebar layouts</li>
                    <li><strong>Tablet:</strong> Condensed navigation, stacked content</li>
                    <li><strong>Mobile:</strong> Hamburger menu, single-column layout</li>
                </ul>

                <h3>Touch-Friendly Interface</h3>
                <p>Mobile-specific features:</p>
                <ul>
                    <li><strong>Large Touch Targets:</strong> Easy-to-tap buttons and links</li>
                    <li><strong>Swipe Gestures:</strong> Navigate through image galleries</li>
                    <li><strong>Pull-to-Refresh:</strong> Update content on mobile</li>
                    <li><strong>Optimized Forms:</strong> Mobile keyboard support</li>
                </ul>

                <div class="callout callout-success">
                    <div class="callout-title">
                        <i class="fas fa-lightbulb"></i>
                        Navigation Tips
                    </div>
                    <ul>
                        <li>Use the search bar for quick access to specific properties or cars</li>
                        <li>Bookmark frequently visited pages</li>
                        <li>Use browser back/forward buttons for easy navigation</li>
                        <li>The logo always returns you to the homepage</li>
                    </ul>
                </div>
            </div>
        `;
    }

    // Additional section content methods would go here...
    // For brevity, I'll add a few more key sections

    getPropertyListingsContent() {
        return `
            <div class="section" id="section-03">
                <div class="section-header">
                    <div class="section-number">03</div>
                    <h1 class="section-title">Property Listings</h1>
                    <p class="section-subtitle">Browse properties, understand booking process, and learn how to create property listings</p>
                </div>

                <h2>Browsing Properties</h2>
                
                <div class="content-grid">
                    <div class="content-card">
                        <i class="fas fa-search card-icon"></i>
                        <h3 class="card-title">Accessing Property Listings</h3>
                        <ol>
                            <li>Click "Properties" in the main navigation menu</li>
                            <li>Use the search bar on the homepage</li>
                            <li>Click "Explore" from the hero section</li>
                        </ol>
                    </div>

                    <div class="content-card">
                        <i class="fas fa-filter card-icon"></i>
                        <h3 class="card-title">Search and Filters</h3>
                        <p>Use advanced filters to find the perfect property:</p>
                        <ul>
                            <li>Location and dates</li>
                            <li>Price range</li>
                            <li>Property type</li>
                            <li>Amenities and features</li>
                        </ul>
                    </div>
                </div>

                <div class="image-placeholder">
                    <i class="fas fa-image"></i>
                    <div class="placeholder-text">Properties Listing Page</div>
                    <div class="placeholder-description">Screenshot of the main properties page with search filters and property cards</div>
                </div>

                <h2>Property Details Page</h2>

                <h3>Information Sections</h3>
                <ol class="step-list">
                    <li class="step-item"><strong>Image Gallery:</strong> High-quality photos and virtual tours</li>
                    <li class="step-item"><strong>Basic Information:</strong> Property details, location, and host info</li>
                    <li class="step-item"><strong>Description:</strong> Detailed property and neighborhood information</li>
                    <li class="step-item"><strong>Amenities:</strong> Complete list of available features</li>
                    <li class="step-item"><strong>Location & Map:</strong> Interactive map and nearby attractions</li>
                    <li class="step-item"><strong>Reviews:</strong> Guest reviews and ratings</li>
                </ol>

                <h2>Booking Process</h2>

                <div class="callout callout-info">
                    <div class="callout-title">
                        <i class="fas fa-calendar-check"></i>
                        Booking Steps
                    </div>
                    <ol>
                        <li>Select dates and number of guests</li>
                        <li>Review booking details and pricing</li>
                        <li>Provide guest information</li>
                        <li>Choose payment method</li>
                        <li>Confirm booking and receive confirmation</li>
                    </ol>
                </div>

                <h2>Creating Property Listings (For Hosts)</h2>

                <h3>Prerequisites</h3>
                <p>To list a property, you must:</p>
                <ul>
                    <li>Have a verified account</li>
                    <li>Complete identity verification</li>
                    <li>Agree to host terms and conditions</li>
                </ul>

                <h3>Listing Creation Process</h3>
                <ol class="step-list">
                    <li class="step-item"><strong>Property Basics:</strong> Title, type, location, description</li>
                    <li class="step-item"><strong>Capacity and Rooms:</strong> Guests, bedrooms, bathrooms</li>
                    <li class="step-item"><strong>Location:</strong> Address verification and map setup</li>
                    <li class="step-item"><strong>Amenities:</strong> Select all available features</li>
                    <li class="step-item"><strong>Photos:</strong> Upload high-quality images</li>
                    <li class="step-item"><strong>Pricing:</strong> Set rates and fees</li>
                    <li class="step-item"><strong>Availability:</strong> Calendar and house rules</li>
                    <li class="step-item"><strong>Review:</strong> Final review and publish</li>
                </ol>

                <div class="callout callout-success">
                    <div class="callout-title">
                        <i class="fas fa-star"></i>
                        Pro Tips for Hosts
                    </div>
                    <ul>
                        <li>Use high-quality, well-lit photos</li>
                        <li>Write detailed, honest descriptions</li>
                        <li>Respond quickly to booking inquiries</li>
                        <li>Keep your calendar updated</li>
                        <li>Provide clear check-in instructions</li>
                    </ul>
                </div>
            </div>
        `;
    }

    // Continue with other section methods...
    // For brevity, I'll implement the key methods and add placeholders for others

    updateProgress() {
        const progressFill = document.querySelector('.progress-fill');
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        if (progressFill) {
            progressFill.style.width = `${Math.min(scrollPercent, 100)}%`;
        }
    }

    setupIntersectionObserver() {
        const options = {
            root: null,
            rootMargin: '0px',
            threshold: 0.1
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, options);

        // Observe content cards and other elements
        document.querySelectorAll('.content-card, .callout, .step-item').forEach(el => {
            observer.observe(el);
        });
    }

    handleKeyboardNavigation(e) {
        // Arrow key navigation
        if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
            this.navigateToPreviousSection();
        } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
            this.navigateToNextSection();
        }
        
        // Escape to close navigation
        if (e.key === 'Escape' && this.isNavOpen) {
            this.toggleNavigation();
        }
    }

    navigateToPreviousSection() {
        const sections = ['cover', 'toc', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
        const currentIndex = sections.indexOf(this.currentSection);
        if (currentIndex > 0) {
            this.navigateToSection(sections[currentIndex - 1]);
        }
    }

    navigateToNextSection() {
        const sections = ['cover', 'toc', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
        const currentIndex = sections.indexOf(this.currentSection);
        if (currentIndex < sections.length - 1) {
            this.navigateToSection(sections[currentIndex + 1]);
        }
    }

    handleResize() {
        // Close navigation on mobile when resizing to desktop
        if (window.innerWidth > 768 && this.isNavOpen) {
            this.toggleNavigation();
        }
    }

    setupPrintHandlers() {
        // Add print button functionality
        const printBtn = document.createElement('button');
        printBtn.innerHTML = '<i class="fas fa-print"></i> Print Manual';
        printBtn.className = 'print-btn';
        printBtn.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            box-shadow: var(--shadow-medium);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: var(--transition);
        `;
        
        printBtn.addEventListener('click', () => {
            window.print();
        });
        
        printBtn.addEventListener('mouseenter', () => {
            printBtn.style.transform = 'scale(1.05)';
            printBtn.style.background = 'var(--primary-dark)';
        });
        
        printBtn.addEventListener('mouseleave', () => {
            printBtn.style.transform = 'scale(1)';
            printBtn.style.background = 'var(--primary-color)';
        });
        
        document.body.appendChild(printBtn);
    }

    loadSections() {
        // This would typically load section data from external files
        // For now, we'll use the embedded content
        this.sections = [
            'cover', 'toc', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'
        ];
    }

    // Placeholder methods for other sections
    getCarRentalsContent() { return '<div class="section"><h1>Car Rentals - Content Coming Soon</h1></div>'; }
    getHotelBookingsContent() { return '<div class="section"><h1>Hotel Bookings - Content Coming Soon</h1></div>'; }
    getBookingManagementContent() { return '<div class="section"><h1>Booking Management - Content Coming Soon</h1></div>'; }
    getPaymentSystemContent() { return '<div class="section"><h1>Payment System - Content Coming Soon</h1></div>'; }
    getProfileManagementContent() { return '<div class="section"><h1>Profile Management - Content Coming Soon</h1></div>'; }
    getHostFeaturesContent() { return '<div class="section"><h1>Host Features - Content Coming Soon</h1></div>'; }
    getAdminFeaturesContent() { return '<div class="section"><h1>Admin Features - Content Coming Soon</h1></div>'; }
    getLegalPoliciesContent() { return '<div class="section"><h1>Legal & Policies - Content Coming Soon</h1></div>'; }
    getTroubleshootingContent() { return '<div class="section"><h1>Troubleshooting & Support - Content Coming Soon</h1></div>'; }
}

// Initialize the manual when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new UserManual();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserManual;
}
