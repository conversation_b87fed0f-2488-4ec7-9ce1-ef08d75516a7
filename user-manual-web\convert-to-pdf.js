// PDF Conversion Script for Gesco Stay User Manual
// This script uses <PERSON><PERSON>pet<PERSON> to convert HTML sections to PDF

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class PDFConverter {
    constructor() {
        this.sections = [
            { file: '01-getting-started.html', title: '01 - Getting Started' },
            { file: '02-navigation-interface.html', title: '02 - Navigation & Interface' },
            { file: '03-property-listings.html', title: '03 - Property Listings' },
            { file: '04-car-rentals.html', title: '04 - Car Rentals' },
            { file: '05-hotel-bookings.html', title: '05 - Hotel Bookings' },
            { file: '06-booking-management.html', title: '06 - Booking Management' },
            { file: '07-payment-system.html', title: '07 - Payment System' },
            { file: '08-profile-management.html', title: '08 - Profile Management' },
            { file: '09-host-features.html', title: '09 - Host Features' },
            { file: '10-admin-features.html', title: '10 - Admin Features' },
            { file: '11-legal-policies.html', title: '11 - Legal & Policies' },
            { file: '12-troubleshooting-support.html', title: '12 - Troubleshooting & Support' }
        ];
        
        this.outputDir = './pdf-output';
        this.sectionsDir = './sections';
    }

    async init() {
        // Create output directory if it doesn't exist
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir);
        }

        console.log('🚀 Starting PDF conversion process...');
        console.log(`📁 Output directory: ${this.outputDir}`);
    }

    async convertSectionToPDF(section) {
        const browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        try {
            const page = await browser.newPage();
            
            // Set viewport for consistent rendering
            await page.setViewport({
                width: 1200,
                height: 800,
                deviceScaleFactor: 2
            });

            const filePath = path.join(this.sectionsDir, section.file);
            const fileUrl = `file://${path.resolve(filePath)}`;
            
            console.log(`📄 Converting: ${section.title}`);
            
            // Check if file exists
            if (!fs.existsSync(filePath)) {
                console.log(`⚠️  File not found: ${filePath}`);
                return false;
            }

            await page.goto(fileUrl, {
                waitUntil: 'networkidle0',
                timeout: 30000
            });

            // Wait for fonts to load
            await page.evaluateHandle('document.fonts.ready');

            // Generate PDF
            const pdfPath = path.join(this.outputDir, `${section.file.replace('.html', '.pdf')}`);
            
            await page.pdf({
                path: pdfPath,
                format: 'A4',
                margin: {
                    top: '2cm',
                    right: '1.5cm',
                    bottom: '2cm',
                    left: '1.5cm'
                },
                printBackground: true,
                preferCSSPageSize: true,
                displayHeaderFooter: true,
                headerTemplate: `
                    <div style="font-size: 10px; color: #8B4513; width: 100%; text-align: center; margin: 0 auto;">
                        <span>Gesco Stay User Manual - ${section.title}</span>
                    </div>
                `,
                footerTemplate: `
                    <div style="font-size: 9px; color: #666; width: 100%; display: flex; justify-content: space-between; margin: 0 1.5cm;">
                        <span>© 2025 Gesco Stay</span>
                        <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
                    </div>
                `
            });

            console.log(`✅ Generated: ${pdfPath}`);
            return true;

        } catch (error) {
            console.error(`❌ Error converting ${section.title}:`, error.message);
            return false;
        } finally {
            await browser.close();
        }
    }

    async convertCoverPage() {
        const browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        try {
            const page = await browser.newPage();
            
            await page.setViewport({
                width: 1200,
                height: 800,
                deviceScaleFactor: 2
            });

            // Create a simple cover page HTML
            const coverHTML = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Gesco Stay User Manual - Cover</title>
                    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                    <style>
                        body {
                            font-family: 'Inter', sans-serif;
                            margin: 0;
                            padding: 0;
                            background: linear-gradient(135deg, #8B4513 0%, #5D2F0A 100%);
                            color: white;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            min-height: 100vh;
                            text-align: center;
                        }
                        .logo { font-size: 3rem; margin-bottom: 2rem; }
                        .title { font-size: 4rem; font-weight: 700; margin-bottom: 1rem; }
                        .subtitle { font-size: 2.5rem; font-weight: 400; color: #CD853F; margin-bottom: 3rem; }
                        .description { font-size: 1.25rem; max-width: 600px; margin-bottom: 3rem; opacity: 0.9; }
                        .details { font-size: 1.1rem; }
                        .details div { margin: 0.5rem 0; }
                        .footer { position: absolute; bottom: 2rem; font-size: 0.9rem; opacity: 0.8; }
                    </style>
                </head>
                <body>
                    <div class="logo">🏠 GESCO STAY</div>
                    <h1 class="title">Complete User Manual</h1>
                    <h2 class="subtitle">Web Platform Guide</h2>
                    <p class="description">Your comprehensive guide to using the Gesco Stay travel and accommodation platform</p>
                    <div class="details">
                        <div>📅 Version 1.0 - August 2025</div>
                        <div>🌐 Web Platform Documentation</div>
                        <div>👥 For All Users, Hosts & Administrators</div>
                    </div>
                    <div class="footer">
                        <p>© 2025 Gesco Stay. All rights reserved.</p>
                        <p>Africa's Most Trusted Travel Platform</p>
                    </div>
                </body>
                </html>
            `;

            await page.setContent(coverHTML, { waitUntil: 'networkidle0' });
            await page.evaluateHandle('document.fonts.ready');

            const pdfPath = path.join(this.outputDir, '00-cover.pdf');
            
            await page.pdf({
                path: pdfPath,
                format: 'A4',
                margin: { top: 0, right: 0, bottom: 0, left: 0 },
                printBackground: true,
                preferCSSPageSize: true
            });

            console.log(`✅ Generated cover page: ${pdfPath}`);
            return true;

        } catch (error) {
            console.error('❌ Error creating cover page:', error.message);
            return false;
        } finally {
            await browser.close();
        }
    }

    async convertTableOfContents() {
        const browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        try {
            const page = await browser.newPage();
            
            await page.setViewport({
                width: 1200,
                height: 800,
                deviceScaleFactor: 2
            });

            const fileUrl = `file://${path.resolve('./index.html')}`;
            await page.goto(fileUrl, { waitUntil: 'networkidle0' });

            // Navigate to TOC section
            await page.evaluate(() => {
                document.getElementById('cover').style.display = 'none';
                document.getElementById('toc').style.display = 'block';
                document.getElementById('contentContainer').innerHTML = '';
            });

            await page.evaluateHandle('document.fonts.ready');

            const pdfPath = path.join(this.outputDir, '00-table-of-contents.pdf');
            
            await page.pdf({
                path: pdfPath,
                format: 'A4',
                margin: {
                    top: '2cm',
                    right: '1.5cm',
                    bottom: '2cm',
                    left: '1.5cm'
                },
                printBackground: true,
                preferCSSPageSize: true,
                displayHeaderFooter: true,
                headerTemplate: `
                    <div style="font-size: 10px; color: #8B4513; width: 100%; text-align: center;">
                        <span>Gesco Stay User Manual - Table of Contents</span>
                    </div>
                `,
                footerTemplate: `
                    <div style="font-size: 9px; color: #666; width: 100%; display: flex; justify-content: space-between; margin: 0 1.5cm;">
                        <span>© 2025 Gesco Stay</span>
                        <span>Page <span class="pageNumber"></span></span>
                    </div>
                `
            });

            console.log(`✅ Generated table of contents: ${pdfPath}`);
            return true;

        } catch (error) {
            console.error('❌ Error creating table of contents:', error.message);
            return false;
        } finally {
            await browser.close();
        }
    }

    async convertAll() {
        await this.init();

        let successCount = 0;
        let totalCount = 0;

        // Convert cover page
        console.log('\n📖 Creating cover page...');
        if (await this.convertCoverPage()) successCount++;
        totalCount++;

        // Convert table of contents
        console.log('\n📋 Creating table of contents...');
        if (await this.convertTableOfContents()) successCount++;
        totalCount++;

        // Convert all sections
        console.log('\n📚 Converting sections...');
        for (const section of this.sections) {
            if (await this.convertSectionToPDF(section)) {
                successCount++;
            }
            totalCount++;
        }

        // Summary
        console.log('\n📊 Conversion Summary:');
        console.log(`✅ Successfully converted: ${successCount}/${totalCount} files`);
        console.log(`📁 Output location: ${path.resolve(this.outputDir)}`);
        
        if (successCount === totalCount) {
            console.log('\n🎉 All files converted successfully!');
            console.log('\n📝 Next steps:');
            console.log('1. Review the generated PDF files');
            console.log('2. Merge them using a PDF merger tool');
            console.log('3. Add bookmarks for easy navigation');
        } else {
            console.log(`\n⚠️  ${totalCount - successCount} files failed to convert`);
            console.log('Check the error messages above for details');
        }
    }

    async convertSingle(sectionNumber) {
        await this.init();
        
        const section = this.sections.find(s => s.file.startsWith(sectionNumber));
        if (!section) {
            console.error(`❌ Section ${sectionNumber} not found`);
            return;
        }

        console.log(`\n📄 Converting single section: ${section.title}`);
        const success = await this.convertSectionToPDF(section);
        
        if (success) {
            console.log(`✅ Successfully converted ${section.title}`);
        } else {
            console.log(`❌ Failed to convert ${section.title}`);
        }
    }
}

// Command line interface
async function main() {
    const converter = new PDFConverter();
    
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        // Convert all sections
        await converter.convertAll();
    } else if (args[0] === 'cover') {
        // Convert only cover page
        await converter.convertCoverPage();
    } else if (args[0] === 'toc') {
        // Convert only table of contents
        await converter.convertTableOfContents();
    } else {
        // Convert specific section
        await converter.convertSingle(args[0]);
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

module.exports = PDFConverter;
