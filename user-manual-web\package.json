{"name": "gesco-stay-user-manual", "version": "1.0.0", "description": "Gesco Stay Web Platform User Manual - PDF Conversion Tools", "main": "convert-to-pdf.js", "scripts": {"convert": "node convert-to-pdf.js", "convert:all": "node convert-to-pdf.js", "convert:cover": "node convert-to-pdf.js cover", "convert:toc": "node convert-to-pdf.js toc", "convert:section": "node convert-to-pdf.js", "serve": "npx http-server . -p 8080 -o", "build": "npm run convert:all", "clean": "rm -rf pdf-output && mkdir pdf-output", "help": "echo 'Available commands:\n  npm run convert - Convert all sections to PDF\n  npm run convert:cover - Convert cover page only\n  npm run convert:toc - Convert table of contents only\n  npm run convert:section 01 - Convert specific section\n  npm run serve - Start local web server\n  npm run clean - Clean output directory'"}, "keywords": ["user-manual", "documentation", "pdf-conversion", "gesco-stay", "travel-platform"], "author": "Gesco Stay", "license": "UNLICENSED", "private": true, "dependencies": {"puppeteer": "^21.0.0"}, "devDependencies": {"http-server": "^14.1.1"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "private"}, "bugs": {"url": "mailto:<EMAIL>"}, "homepage": "https://gescostay.com"}