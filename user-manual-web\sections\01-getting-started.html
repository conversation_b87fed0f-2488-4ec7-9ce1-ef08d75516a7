<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Getting Started - Gesco Stay User Manual</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/print.css" media="print">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="section" id="section-01">
        <div class="section-header">
            <div class="section-number">01</div>
            <h1 class="section-title">Getting Started</h1>
            <p class="section-subtitle">Learn the basics of using Gesco Stay, including account creation, verification, and initial setup</p>
        </div>

        <div class="content-grid">
            <div class="content-card">
                <i class="fas fa-globe card-icon"></i>
                <h3 class="card-title">Platform Overview</h3>
                <p>Gesco Stay is Africa's most trusted travel platform connecting travelers with authentic local experiences across The Gambia and West Africa.</p>
                <ul>
                    <li><strong>Property Rentals:</strong> Authentic local homes and accommodations</li>
                    <li><strong>Car Rentals:</strong> Reliable transportation options</li>
                    <li><strong>Hotel Bookings:</strong> Professional hotel accommodations</li>
                    <li><strong>Local Experiences:</strong> Cultural tours and authentic experiences</li>
                    <li><strong>Nightlife & Entertainment:</strong> Access to clubs, lounges, and cultural events</li>
                </ul>
            </div>

            <div class="content-card">
                <i class="fas fa-user-plus card-icon"></i>
                <h3 class="card-title">Quick Start Guide</h3>
                <ol class="step-list">
                    <li class="step-item">Create your account using email or phone number</li>
                    <li class="step-item">Verify your account through OTP verification</li>
                    <li class="step-item">Complete your profile with personal information</li>
                    <li class="step-item">Start browsing properties, cars, and hotels</li>
                    <li class="step-item">Make your first booking</li>
                </ol>
            </div>
        </div>

        <div class="image-placeholder">
            <i class="fas fa-image"></i>
            <div class="placeholder-text">Platform Overview Screenshot</div>
            <div class="placeholder-description">Screenshot of the homepage showing all available services and main navigation</div>
        </div>

        <h2>Account Registration</h2>
        
        <div class="callout callout-info">
            <div class="callout-title">
                <i class="fas fa-info-circle"></i>
                Registration Requirements
            </div>
            <p>You must be 18 years or older to create an account. You'll need a valid email address or mobile phone number for verification.</p>
        </div>

        <h3>Step 1: Access Registration</h3>
        <ol class="step-list">
            <li class="step-item">Visit the Gesco Stay website</li>
            <li class="step-item">Click the "Sign Up" button in the top navigation</li>
            <li class="step-item">You'll be redirected to the registration page</li>
        </ol>

        <div class="image-placeholder">
            <i class="fas fa-user-plus"></i>
            <div class="placeholder-text">Registration Button Location</div>
            <div class="placeholder-description">Screenshot showing the Sign Up button location in the navigation</div>
        </div>

        <h3>Step 2: Choose Registration Method</h3>
        <p>You can register using either:</p>
        <div class="content-grid">
            <div class="content-card">
                <i class="fas fa-envelope card-icon"></i>
                <h4 class="card-title">Email Registration</h4>
                <p>Use your email address for account creation. This is recommended for users who prefer email communication.</p>
            </div>
            <div class="content-card">
                <i class="fas fa-mobile-alt card-icon"></i>
                <h4 class="card-title">Phone Registration</h4>
                <p>Use your mobile phone number with country code (+220 for Gambia). This enables SMS notifications and faster verification.</p>
            </div>
        </div>

        <div class="image-placeholder">
            <i class="fas fa-mobile-alt"></i>
            <div class="placeholder-text">Registration Method Selection</div>
            <div class="placeholder-description">Screenshot showing email/phone selection options</div>
        </div>

        <h3>Step 3: Fill Registration Form</h3>
        <p>The registration form includes the following fields in order:</p>
        <ol>
            <li><strong>First Name:</strong> Your given name</li>
            <li><strong>Last Name:</strong> Your family name</li>
            <li><strong>Mobile Number:</strong> Phone number with country code (+220 for Gambia)</li>
            <li><strong>Email Address:</strong> Your email address</li>
            <li><strong>Password:</strong> Secure password (minimum 8 characters)</li>
            <li><strong>Confirm Password:</strong> Re-enter your password</li>
        </ol>

        <div class="image-placeholder">
            <i class="fas fa-form"></i>
            <div class="placeholder-text">Complete Registration Form</div>
            <div class="placeholder-description">Screenshot of the registration form with all fields visible</div>
        </div>

        <div class="callout callout-warning">
            <div class="callout-title">
                <i class="fas fa-exclamation-triangle"></i>
                Password Security
            </div>
            <p>Create a strong password with at least 8 characters, including:</p>
            <ul>
                <li>Uppercase letters (A-Z)</li>
                <li>Lowercase letters (a-z)</li>
                <li>Numbers (0-9)</li>
                <li>Special characters (!@#$%^&*)</li>
            </ul>
        </div>

        <h2>Account Verification</h2>

        <h3>OTP Verification Process</h3>
        <p>After successful registration, you'll be redirected to the OTP verification page:</p>
        <ol class="step-list">
            <li class="step-item"><strong>Check Your Messages:</strong> An OTP code will be sent to your registered mobile number or email</li>
            <li class="step-item"><strong>Enter OTP Code:</strong> Input the 6-digit verification code</li>
            <li class="step-item"><strong>Verify Account:</strong> Click "Verify" to complete the process</li>
        </ol>

        <div class="image-placeholder">
            <i class="fas fa-key"></i>
            <div class="placeholder-text">OTP Verification Page</div>
            <div class="placeholder-description">Screenshot of OTP verification interface with code input field</div>
        </div>

        <div class="callout callout-info">
            <div class="callout-title">
                <i class="fas fa-clock"></i>
                Important Notes
            </div>
            <ul>
                <li>OTP codes expire after 10 minutes</li>
                <li>You can request a new code if needed</li>
                <li>Verification is required before accessing protected features</li>
                <li>Check spam/junk folders if you don't receive the code</li>
            </ul>
        </div>

        <h2>Login Process</h2>

        <div class="content-grid">
            <div class="content-card">
                <i class="fas fa-envelope card-icon"></i>
                <h3 class="card-title">Email Login</h3>
                <ol>
                    <li>Click "Sign In" from the homepage</li>
                    <li>Select "Email" as login method</li>
                    <li>Enter your email address and password</li>
                    <li>Click "Sign In"</li>
                </ol>
            </div>

            <div class="content-card">
                <i class="fas fa-mobile-alt card-icon"></i>
                <h3 class="card-title">Phone Number Login</h3>
                <ol>
                    <li>Click "Sign In" from the homepage</li>
                    <li>Select "Phone" as login method</li>
                    <li>Enter your phone number with country code</li>
                    <li>Enter your password and click "Sign In"</li>
                </ol>
            </div>
        </div>

        <div class="image-placeholder">
            <i class="fas fa-sign-in-alt"></i>
            <div class="placeholder-text">Login Interface</div>
            <div class="placeholder-description">Screenshot of login form showing email and phone login options</div>
        </div>

        <h3>Forgot Password</h3>
        <p>If you forget your password:</p>
        <ol class="step-list">
            <li class="step-item">Click "Forgot Password?" on the login page</li>
            <li class="step-item">Enter your email address</li>
            <li class="step-item">Check your email for reset instructions</li>
            <li class="step-item">Follow the link to create a new password</li>
        </ol>

        <h2>Profile Setup</h2>

        <h3>Initial Profile Configuration</h3>
        <p>After first login, complete your profile:</p>
        <ul>
            <li><strong>Profile Picture:</strong> Upload a clear photo of yourself</li>
            <li><strong>Personal Information:</strong> Verify your name and contact details</li>
            <li><strong>Preferences:</strong> Set your travel preferences and interests</li>
            <li><strong>Verification:</strong> Complete identity verification if required</li>
        </ul>

        <div class="image-placeholder">
            <i class="fas fa-user-cog"></i>
            <div class="placeholder-text">Profile Setup Page</div>
            <div class="placeholder-description">Screenshot of profile setup interface with all configuration options</div>
        </div>

        <h3>Verification Status</h3>
        <p>Your account may have different verification levels:</p>
        <table>
            <thead>
                <tr>
                    <th>Verification Type</th>
                    <th>Status</th>
                    <th>Benefits</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Email Verified</strong></td>
                    <td>✅ Email address confirmed</td>
                    <td>Required for booking and hosting</td>
                </tr>
                <tr>
                    <td><strong>Phone Verified</strong></td>
                    <td>✅ Phone number confirmed</td>
                    <td>Enhanced security and communication</td>
                </tr>
                <tr>
                    <td><strong>Identity Verified</strong></td>
                    <td>✅ Government ID verified</td>
                    <td>Required for hosting, enhanced trust</td>
                </tr>
                <tr>
                    <td><strong>Payment Verified</strong></td>
                    <td>✅ Payment method added</td>
                    <td>Faster booking process</td>
                </tr>
            </tbody>
        </table>

        <h2>Security Features</h2>

        <h3>Two-Factor Authentication</h3>
        <p>For enhanced security:</p>
        <ol class="step-list">
            <li class="step-item">Go to Profile Settings</li>
            <li class="step-item">Enable Two-Factor Authentication</li>
            <li class="step-item">Follow the setup instructions</li>
        </ol>

        <h3>Account Security Tips</h3>
        <ul>
            <li>Use a strong, unique password</li>
            <li>Enable two-factor authentication</li>
            <li>Keep your contact information updated</li>
            <li>Log out from shared devices</li>
            <li>Report suspicious activity immediately</li>
        </ul>

        <div class="callout callout-success">
            <div class="callout-title">
                <i class="fas fa-check-circle"></i>
                Next Steps
            </div>
            <p>Once your account is set up:</p>
            <ul>
                <li><strong>Explore the Platform:</strong> Browse available properties, cars, and hotels</li>
                <li><strong>Complete Your Profile:</strong> Add more details for better recommendations</li>
                <li><strong>Start Booking:</strong> Make your first reservation</li>
                <li><strong>Consider Hosting:</strong> List your property or car to earn income</li>
            </ul>
        </div>

        <div class="callout callout-info">
            <div class="callout-title">
                <i class="fas fa-question-circle"></i>
                Need Help?
            </div>
            <p>If you need assistance:</p>
            <ul>
                <li><strong>Email:</strong> <EMAIL></li>
                <li><strong>Live Chat:</strong> Available on the platform</li>
                <li><strong>Emergency Support:</strong> 24/7 for safety-related issues</li>
            </ul>
        </div>
    </div>
</body>
</html>
