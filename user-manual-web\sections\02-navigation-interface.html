<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation & Interface - Gesco Stay User Manual</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/print.css" media="print">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="section" id="section-02">
        <div class="section-header">
            <div class="section-number">02</div>
            <h1 class="section-title">Navigation & Interface</h1>
            <p class="section-subtitle">Understand the platform's user interface, navigation menu, and search functionality</p>
        </div>

        <h2>Homepage Tour</h2>
        
        <div class="content-grid">
            <div class="content-card">
                <i class="fas fa-home card-icon"></i>
                <h3 class="card-title">Header Section</h3>
                <p>The homepage header contains:</p>
                <ul>
                    <li><strong>Gesco Stay Logo:</strong> Click to return to homepage</li>
                    <li><strong>Navigation Menu:</strong> Access to main platform sections</li>
                    <li><strong>User Account:</strong> Login/Profile access</li>
                    <li><strong>Language/Currency:</strong> Platform settings</li>
                </ul>
            </div>

            <div class="content-card">
                <i class="fas fa-star card-icon"></i>
                <h3 class="card-title">Hero Section</h3>
                <p>The main hero section features:</p>
                <ul>
                    <li><strong>Welcome Message:</strong> Platform introduction</li>
                    <li><strong>Search Bar:</strong> Quick property/car search</li>
                    <li><strong>Call-to-Action Buttons:</strong> "Start Hosting" and "Explore"</li>
                    <li><strong>Featured Images:</strong> Showcase of available properties</li>
                </ul>
            </div>
        </div>

        <div class="image-placeholder">
            <i class="fas fa-image"></i>
            <div class="placeholder-text">Homepage Header and Hero Section</div>
            <div class="placeholder-description">Screenshot of the main homepage showing header, navigation, and hero section with search functionality</div>
        </div>

        <h3>Categories Section</h3>
        <p>Explore different service categories:</p>
        <div class="content-grid">
            <div class="content-card">
                <i class="fas fa-building card-icon"></i>
                <h4 class="card-title">Properties</h4>
                <p>Residential accommodations and authentic local homes</p>
            </div>
            <div class="content-card">
                <i class="fas fa-hotel card-icon"></i>
                <h4 class="card-title">Hotels</h4>
                <p>Professional hotel bookings and accommodations</p>
            </div>
            <div class="content-card">
                <i class="fas fa-car card-icon"></i>
                <h4 class="card-title">Car Rentals</h4>
                <p>Vehicle rental services for transportation needs</p>
            </div>
            <div class="content-card">
                <i class="fas fa-utensils card-icon"></i>
                <h4 class="card-title">Restaurants</h4>
                <p>Dining experiences (external link to Gesco platform)</p>
            </div>
            <div class="content-card">
                <i class="fas fa-music card-icon"></i>
                <h4 class="card-title">Night Life</h4>
                <p>Entertainment venues and cultural events</p>
            </div>
        </div>

        <div class="image-placeholder">
            <i class="fas fa-th-large"></i>
            <div class="placeholder-text">Service Categories Section</div>
            <div class="placeholder-description">Screenshot showing all service categories with icons and descriptions</div>
        </div>

        <h2>Navigation Menu</h2>

        <h3>Primary Navigation Items</h3>
        <p>The main navigation includes:</p>
        <ol>
            <li><strong>Properties:</strong> Browse residential listings and accommodations</li>
            <li><strong>Hotels:</strong> Professional hotel accommodations and bookings</li>
            <li><strong>Car Rentals:</strong> Vehicle rental options and services</li>
        </ol>

        <h3>Secondary Navigation Items</h3>
        <p>Additional navigation options:</p>
        <ol>
            <li><strong>Restaurants:</strong> External link to Gesco restaurant platform</li>
            <li><strong>Night Life:</strong> External link to entertainment venues and events</li>
            <li><strong>About:</strong> Information about the Gesco Stay platform</li>
        </ol>

        <h3>User-Specific Navigation</h3>
        <p>When logged in, additional options appear:</p>
        <ul>
            <li><strong>List Property:</strong> Create a property listing for hosting</li>
            <li><strong>List Hotel:</strong> Create a hotel listing for business</li>
            <li><strong>List Car:</strong> Create a car rental listing</li>
            <li><strong>My Bookings:</strong> View and manage your reservations</li>
            <li><strong>Profile:</strong> Account management and settings</li>
        </ul>

        <div class="image-placeholder">
            <i class="fas fa-bars"></i>
            <div class="placeholder-text">Expanded Navigation Menu</div>
            <div class="placeholder-description">Screenshot of the full navigation menu showing all available options</div>
        </div>

        <div class="callout callout-info">
            <div class="callout-title">
                <i class="fas fa-mobile-alt"></i>
                Responsive Navigation
            </div>
            <p>On smaller screens:</p>
            <ul>
                <li>Navigation collapses into a hamburger menu</li>
                <li>Items that don't fit are moved to a "More" dropdown</li>
                <li>Text size remains consistent across all screen sizes</li>
                <li>Touch-friendly interface for mobile devices</li>
            </ul>
        </div>

        <div class="image-placeholder">
            <i class="fas fa-mobile-alt"></i>
            <div class="placeholder-text">Mobile Navigation Menu</div>
            <div class="placeholder-description">Screenshot of mobile hamburger menu and responsive navigation</div>
        </div>

        <h2>Search Functionality</h2>

        <h3>Property Search</h3>
        <p>The main search bar allows you to:</p>
        <ol class="step-list">
            <li class="step-item"><strong>Enter Location:</strong> Type city, area, or specific address</li>
            <li class="step-item"><strong>Select Dates:</strong> Check-in and check-out dates using date picker</li>
            <li class="step-item"><strong>Choose Guests:</strong> Number of guests staying</li>
            <li class="step-item"><strong>Apply Filters:</strong> Price range, amenities, property type</li>
        </ol>

        <div class="image-placeholder">
            <i class="fas fa-search"></i>
            <div class="placeholder-text">Property Search Interface</div>
            <div class="placeholder-description">Screenshot of the property search form with location, dates, and guest selection</div>
        </div>

        <h3>Advanced Filters</h3>
        <p>Available filter options for properties:</p>
        <div class="content-grid">
            <div class="content-card">
                <i class="fas fa-dollar-sign card-icon"></i>
                <h4 class="card-title">Price & Budget</h4>
                <ul>
                    <li>Minimum and maximum price range</li>
                    <li>Currency selection</li>
                    <li>Price per night or total cost</li>
                </ul>
            </div>
            <div class="content-card">
                <i class="fas fa-home card-icon"></i>
                <h4 class="card-title">Property Type</h4>
                <ul>
                    <li>House, apartment, villa</li>
                    <li>Studio, guesthouse</li>
                    <li>Unique properties</li>
                </ul>
            </div>
            <div class="content-card">
                <i class="fas fa-bed card-icon"></i>
                <h4 class="card-title">Rooms & Space</h4>
                <ul>
                    <li>Number of bedrooms (1-5+)</li>
                    <li>Number of bathrooms (1-3+)</li>
                    <li>Guest capacity</li>
                </ul>
            </div>
            <div class="content-card">
                <i class="fas fa-wifi card-icon"></i>
                <h4 class="card-title">Amenities</h4>
                <ul>
                    <li>WiFi, Air Conditioning</li>
                    <li>Kitchen, Parking, Pool</li>
                    <li>Garden, Security, Laundry</li>
                </ul>
            </div>
        </div>

        <div class="image-placeholder">
            <i class="fas fa-filter"></i>
            <div class="placeholder-text">Advanced Search Filters</div>
            <div class="placeholder-description">Screenshot of advanced filter options panel with all available filters</div>
        </div>

        <h3>Car Search</h3>
        <p>For car rentals, the search includes:</p>
        <ol>
            <li><strong>Pickup Location:</strong> Where to collect the car</li>
            <li><strong>Pickup Date & Time:</strong> When you need the car</li>
            <li><strong>Return Date & Time:</strong> When you'll return the car</li>
            <li><strong>Car Type:</strong> Economy, standard, luxury, SUV, etc.</li>
        </ol>

        <div class="image-placeholder">
            <i class="fas fa-car"></i>
            <div class="placeholder-text">Car Search Interface</div>
            <div class="placeholder-description">Screenshot of car rental search form with pickup/return details</div>
        </div>

        <h2>User Interface Elements</h2>

        <div class="content-grid">
            <div class="content-card">
                <i class="fas fa-th-large card-icon"></i>
                <h3 class="card-title">Cards and Listings</h3>
                <p>Property and car listings are displayed as cards containing:</p>
                <ul>
                    <li><strong>Main Image:</strong> Primary photo of property/car</li>
                    <li><strong>Title:</strong> Property name or car model</li>
                    <li><strong>Location:</strong> Address or area</li>
                    <li><strong>Price:</strong> Per night (properties) or per day (cars)</li>
                    <li><strong>Rating:</strong> User reviews and ratings</li>
                    <li><strong>Key Features:</strong> Bedrooms, bathrooms, or car specs</li>
                </ul>
            </div>

            <div class="content-card">
                <i class="fas fa-mouse-pointer card-icon"></i>
                <h3 class="card-title">Buttons and Actions</h3>
                <p>Common button types:</p>
                <ul>
                    <li><strong>Primary Buttons:</strong> Main actions (Book Now, Sign Up)</li>
                    <li><strong>Secondary Buttons:</strong> Alternative actions (View Details, Save)</li>
                    <li><strong>Icon Buttons:</strong> Quick actions (Share, Favorite, Filter)</li>
                </ul>
            </div>
        </div>

        <div class="image-placeholder">
            <i class="fas fa-th-large"></i>
            <div class="placeholder-text">Listing Cards Display</div>
            <div class="placeholder-description">Screenshot showing multiple property and car listing cards with all elements</div>
        </div>

        <h3>Forms and Inputs</h3>
        <p>Form elements include:</p>
        <ul>
            <li><strong>Text Inputs:</strong> Name, email, description fields</li>
            <li><strong>Date Pickers:</strong> Check-in/out dates with calendar interface</li>
            <li><strong>Dropdowns:</strong> Guest count, car type selection</li>
            <li><strong>Checkboxes:</strong> Amenities selection, terms agreement</li>
            <li><strong>File Uploads:</strong> Property images, documents</li>
        </ul>

        <h2>Mobile Experience</h2>

        <h3>Responsive Design</h3>
        <p>The platform adapts to different screen sizes:</p>
        <div class="content-grid">
            <div class="content-card">
                <i class="fas fa-desktop card-icon"></i>
                <h4 class="card-title">Desktop (1200px+)</h4>
                <ul>
                    <li>Full navigation and sidebar layouts</li>
                    <li>Multi-column content display</li>
                    <li>Hover effects and animations</li>
                </ul>
            </div>
            <div class="content-card">
                <i class="fas fa-tablet-alt card-icon"></i>
                <h4 class="card-title">Tablet (768px-1199px)</h4>
                <ul>
                    <li>Condensed navigation menu</li>
                    <li>Stacked content layout</li>
                    <li>Touch-optimized interface</li>
                </ul>
            </div>
            <div class="content-card">
                <i class="fas fa-mobile-alt card-icon"></i>
                <h4 class="card-title">Mobile (767px and below)</h4>
                <ul>
                    <li>Hamburger menu navigation</li>
                    <li>Single-column layout</li>
                    <li>Large touch targets</li>
                </ul>
            </div>
        </div>

        <h3>Touch-Friendly Interface</h3>
        <p>Mobile-specific features:</p>
        <ul>
            <li><strong>Large Touch Targets:</strong> Easy-to-tap buttons and links (minimum 44px)</li>
            <li><strong>Swipe Gestures:</strong> Navigate through image galleries</li>
            <li><strong>Pull-to-Refresh:</strong> Update content on mobile devices</li>
            <li><strong>Optimized Forms:</strong> Mobile keyboard support and input types</li>
            <li><strong>Fast Loading:</strong> Optimized images and content for mobile networks</li>
        </ul>

        <div class="image-placeholder">
            <i class="fas fa-mobile-alt"></i>
            <div class="placeholder-text">Mobile Interface Design</div>
            <div class="placeholder-description">Screenshot of mobile interface showing responsive design and touch-friendly elements</div>
        </div>

        <h2>Accessibility Features</h2>

        <h3>Keyboard Navigation</h3>
        <ul>
            <li><strong>Tab Navigation:</strong> Navigate using Tab key through interactive elements</li>
            <li><strong>Enter/Space:</strong> Activate buttons and links</li>
            <li><strong>Arrow Keys:</strong> Navigate through menus and galleries</li>
            <li><strong>Escape Key:</strong> Close modals and dropdown menus</li>
        </ul>

        <h3>Screen Reader Support</h3>
        <ul>
            <li><strong>Alt Text:</strong> Images include descriptive alternative text</li>
            <li><strong>ARIA Labels:</strong> Interactive elements are properly labeled</li>
            <li><strong>Semantic HTML:</strong> Proper heading structure and landmarks</li>
            <li><strong>Focus Indicators:</strong> Clear visual focus indicators for keyboard users</li>
        </ul>

        <h3>Visual Accessibility</h3>
        <ul>
            <li><strong>High Contrast:</strong> Clear color contrast ratios (WCAG AA compliant)</li>
            <li><strong>Scalable Text:</strong> Text can be enlarged up to 200% without breaking layout</li>
            <li><strong>Color Independence:</strong> Information not conveyed by color alone</li>
        </ul>

        <h2>Performance Features</h2>

        <h3>Fast Loading</h3>
        <ul>
            <li><strong>Optimized Images:</strong> Compressed and properly sized images</li>
            <li><strong>Lazy Loading:</strong> Images load as you scroll down the page</li>
            <li><strong>Caching:</strong> Frequently accessed content is cached for faster loading</li>
            <li><strong>CDN:</strong> Content delivered from global content delivery network</li>
        </ul>

        <h3>Offline Support</h3>
        <ul>
            <li><strong>Service Worker:</strong> Basic offline functionality for key pages</li>
            <li><strong>Cached Content:</strong> Previously viewed content available offline</li>
            <li><strong>Offline Indicators:</strong> Clear indication when offline</li>
        </ul>

        <div class="callout callout-success">
            <div class="callout-title">
                <i class="fas fa-lightbulb"></i>
                Navigation Tips
            </div>
            <ul>
                <li>Use the search bar for quick access to specific properties or cars</li>
                <li>Bookmark frequently visited pages for easy access</li>
                <li>Use browser back/forward buttons for easy navigation</li>
                <li>The Gesco Stay logo always returns you to the homepage</li>
                <li>Use keyboard shortcuts for faster navigation</li>
                <li>Enable location services for better local recommendations</li>
            </ul>
        </div>

        <div class="callout callout-info">
            <div class="callout-title">
                <i class="fas fa-question-circle"></i>
                Interface Help
            </div>
            <p>If you're having trouble with the interface:</p>
            <ul>
                <li>Try refreshing the page</li>
                <li>Clear your browser cache and cookies</li>
                <li>Disable browser extensions temporarily</li>
                <li>Try a different browser or device</li>
                <li>Contact support if issues persist</li>
            </ul>
        </div>
    </div>
</body>
</html>
