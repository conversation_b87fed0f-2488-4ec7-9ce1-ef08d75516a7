# Getting Started with Gesco Stay

Welcome to Gesco Stay, Africa's most trusted travel platform connecting travelers with authentic local experiences across The Gambia and West Africa.

## Platform Overview

Gesco Stay is a comprehensive travel platform that offers:

- **Property Rentals**: Authentic local homes and accommodations
- **Car Rentals**: Reliable transportation options
- **Hotel Bookings**: Professional hotel accommodations
- **Local Experiences**: Cultural tours and authentic local experiences
- **Nightlife & Entertainment**: Access to clubs, lounges, and cultural events

![Platform Overview](./images/platform-overview-placeholder.png)
*Placeholder: Screenshot of the homepage showing all available services*

## Account Registration

### Step 1: Access the Registration Page

1. Visit the Gesco Stay website
2. Click on the "Sign Up" button in the top navigation
3. You'll be redirected to the registration page

![Registration Button](./images/signup-button-placeholder.png)
*Placeholder: Screenshot showing the Sign Up button location*

### Step 2: Choose Registration Method

You can register using either:
- **Email Address**
- **Mobile Phone Number**

![Registration Method Selection](./images/registration-method-placeholder.png)
*Placeholder: Screenshot showing email/phone selection options*

### Step 3: Fill Registration Form

The registration form includes the following fields (in order):

1. **First Name**: Your given name
2. **Last Name**: Your family name
3. **Mobile Number**: Your phone number (with country code +220 for Gambia)
4. **Email Address**: Your email address
5. **Password**: Create a secure password (minimum 8 characters)
6. **Confirm Password**: Re-enter your password

![Registration Form](./images/registration-form-placeholder.png)
*Placeholder: Screenshot of the complete registration form*

### Step 4: Submit Registration

1. Review all entered information
2. Check the "I agree to Terms of Service" checkbox
3. Click "Create Account"

## Account Verification

### OTP Verification Process

After successful registration, you'll be redirected to the OTP verification page:

1. **Check Your Messages**: An OTP code will be sent to your registered mobile number or email
2. **Enter OTP Code**: Input the 6-digit verification code
3. **Verify Account**: Click "Verify" to complete the process

![OTP Verification](./images/otp-verification-placeholder.png)
*Placeholder: Screenshot of OTP verification page*

### Important Notes:
- OTP codes expire after 10 minutes
- You can request a new code if needed
- Verification is required before accessing protected features

## Login Process

### Email Login

1. Click "Sign In" from the homepage
2. Select "Email" as login method
3. Enter your email address and password
4. Click "Sign In"

![Email Login](./images/email-login-placeholder.png)
*Placeholder: Screenshot of email login form*

### Phone Number Login

1. Click "Sign In" from the homepage
2. Select "Phone" as login method
3. Enter your phone number (with country code)
4. Enter your password
5. Click "Sign In"

![Phone Login](./images/phone-login-placeholder.png)
*Placeholder: Screenshot of phone login form*

### Forgot Password

If you forget your password:

1. Click "Forgot Password?" on the login page
2. Enter your email address
3. Check your email for reset instructions
4. Follow the link to create a new password

## Profile Setup

### Initial Profile Configuration

After first login, complete your profile:

1. **Profile Picture**: Upload a clear photo of yourself
2. **Personal Information**: Verify your name and contact details
3. **Preferences**: Set your travel preferences and interests
4. **Verification**: Complete identity verification if required

![Profile Setup](./images/profile-setup-placeholder.png)
*Placeholder: Screenshot of profile setup page*

### Verification Status

Your account may have different verification levels:

- **Email Verified**: ✅ Email address confirmed
- **Phone Verified**: ✅ Phone number confirmed
- **Identity Verified**: ✅ Government ID verified (for hosts)
- **Payment Verified**: ✅ Payment method added

## Security Features

### Two-Factor Authentication

For enhanced security:
1. Go to Profile Settings
2. Enable Two-Factor Authentication
3. Follow the setup instructions

### Account Security Tips

- Use a strong, unique password
- Enable two-factor authentication
- Keep your contact information updated
- Log out from shared devices
- Report suspicious activity immediately

## Next Steps

Once your account is set up:

1. **Explore the Platform**: Browse available properties, cars, and hotels
2. **Complete Your Profile**: Add more details for better recommendations
3. **Start Booking**: Make your first reservation
4. **Consider Hosting**: List your property or car to earn income

---

**Need Help?**
- Email: <EMAIL>
- Live Chat: Available on the platform
- Emergency Support: 24/7 for safety-related issues

[Next: Navigation & Interface →](./02-navigation-interface.md)
