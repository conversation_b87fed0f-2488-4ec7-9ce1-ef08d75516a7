# Payment System

This section covers all aspects of payments on the Gesco Stay platform, including payment methods, processing, security, and refunds.

## Payment Methods

### Available Payment Options

Gesco Stay accepts multiple payment methods to accommodate users across West Africa and internationally:

#### 1. Credit and Debit Cards
- **Visa**: All Visa credit and debit cards
- **Mastercard**: All Mastercard credit and debit cards
- **American Express**: Amex cards (where accepted)
- **Local Cards**: Regional bank cards

![Credit Card Payment](./images/credit-card-payment-placeholder.png)
*Placeholder: Screenshot of credit card payment interface*

#### 2. Wave Payment (Mobile Money)
- **Wave Mobile Money**: Popular in West Africa
- **Local Currency**: Payments in Gambian Dalasi (GMD)
- **Instant Processing**: Real-time payment confirmation
- **Mobile Integration**: Pay directly from mobile wallet

![Wave Payment](./images/wave-payment-placeholder.png)
*Placeholder: Screenshot of Wave payment interface*

#### 3. Bank Transfer
- **Direct Transfer**: Bank-to-bank transfers
- **Local Banks**: Gambian and regional banks
- **International Transfer**: SWIFT transfers for international users
- **Processing Time**: 1-3 business days

#### 4. Platform Credit
- **Gesco Stay Credits**: Earned from cancellations or promotions
- **Refund Credits**: Credits from previous refunds
- **Promotional Credits**: Bonus credits from campaigns
- **Gift Credits**: Credits received as gifts

### Payment Currency

#### Primary Currency
- **Gambian Dalasi (GMD)**: Primary platform currency
- **Real-time Conversion**: Automatic currency conversion
- **Exchange Rates**: Updated daily rates

#### International Payments
- **USD**: US Dollar payments accepted
- **EUR**: Euro payments accepted
- **GBP**: British Pound payments accepted
- **Conversion Fees**: Minimal currency conversion charges

![Currency Selection](./images/currency-selection-placeholder.png)
*Placeholder: Screenshot of currency selection interface*

## Payment Process

### Booking Payment Flow

#### Step 1: Payment Summary
Before payment, review:
- **Booking Details**: Property/car/hotel information
- **Dates**: Check-in/out or rental period
- **Pricing Breakdown**:
  - Base price (per night/day)
  - Service fees
  - Taxes
  - Total amount

![Payment Summary](./images/payment-summary-placeholder.png)
*Placeholder: Screenshot of payment summary page*

#### Step 2: Payment Method Selection
1. Choose your preferred payment method
2. Enter payment details
3. Verify billing information
4. Review terms and conditions

#### Step 3: Payment Processing
1. **Secure Processing**: Encrypted payment transmission
2. **Verification**: Payment method verification
3. **Authorization**: Payment authorization from bank/provider
4. **Confirmation**: Instant payment confirmation

#### Step 4: Booking Confirmation
After successful payment:
- **Immediate Confirmation**: Booking confirmed instantly
- **Confirmation Email**: Detailed booking information sent
- **SMS Notification**: Text message confirmation
- **Booking Reference**: Unique booking ID provided

![Payment Confirmation](./images/payment-confirmation-placeholder.png)
*Placeholder: Screenshot of payment confirmation page*

### Payment Security

#### Security Measures
- **SSL Encryption**: 256-bit SSL encryption for all transactions
- **PCI Compliance**: Payment Card Industry compliance
- **Tokenization**: Card details tokenized for security
- **Fraud Detection**: Advanced fraud prevention systems
- **3D Secure**: Additional authentication for card payments

#### Data Protection
- **No Storage**: Card details not stored on platform
- **Secure Transmission**: Encrypted data transmission
- **Limited Access**: Restricted access to payment data
- **Regular Audits**: Security audits and updates

![Security Features](./images/payment-security-placeholder.png)
*Placeholder: Screenshot highlighting security features*

## Payment Timing and Policies

### When Payments Are Charged

#### Property Bookings
- **Instant Payment**: Full amount charged immediately
- **Booking Confirmation**: Payment confirms reservation
- **Host Payout**: Host paid after guest check-in
- **Refund Window**: Based on cancellation policy

#### Car Rentals
- **Immediate Charge**: Full rental amount charged
- **Security Deposit**: Additional hold on card (if required)
- **Damage Assessment**: Deposit released after return
- **Owner Payout**: Owner paid after rental completion

#### Hotel Bookings
- **Advance Payment**: Full amount or deposit charged
- **Hotel Policy**: Varies by hotel payment terms
- **Guarantee**: Payment guarantees reservation
- **Additional Charges**: Incidentals paid at hotel

### Payment Holds and Deposits

#### Security Deposits
- **Purpose**: Cover potential damages or violations
- **Amount**: Varies by property/vehicle value
- **Hold Duration**: Released after inspection
- **Deduction**: Only if damages confirmed

#### Authorization Holds
- **Temporary Hold**: Verify payment method validity
- **Release Time**: 1-7 business days
- **No Charge**: Hold released if no issues
- **Bank Dependent**: Release time varies by bank

![Payment Holds](./images/payment-holds-placeholder.png)
*Placeholder: Screenshot explaining payment holds*

## Fees and Charges

### Platform Service Fees

#### Guest Service Fees
- **Booking Fee**: Small percentage of booking value
- **Payment Processing**: Credit card processing fees
- **Currency Conversion**: Foreign exchange fees (if applicable)
- **Transparency**: All fees shown before payment

#### Host Service Fees
- **Commission**: Percentage of booking value
- **Payment Processing**: Fees for receiving payments
- **Payout Fees**: Fees for fund transfers
- **Promotional Rates**: Reduced fees for new hosts

### Additional Charges

#### Property Bookings
- **Cleaning Fee**: One-time cleaning charge (set by host)
- **Extra Guest Fee**: Charge for additional guests
- **Pet Fee**: Fee for bringing pets (if allowed)
- **Damage Fee**: Charges for property damage

#### Car Rentals
- **Insurance**: Optional insurance coverage
- **Equipment Rental**: GPS, child seats, etc.
- **Fuel Charges**: Fuel policy violations
- **Late Return**: Penalties for late returns
- **Cleaning Fee**: Excessive cleaning required

#### Hotel Bookings
- **Resort Fees**: Hotel-specific charges
- **Tourism Tax**: Local government taxes
- **Service Charges**: Hotel service fees
- **Incidentals**: Room service, minibar, etc.

![Fee Breakdown](./images/fee-breakdown-placeholder.png)
*Placeholder: Screenshot of detailed fee breakdown*

## Refunds and Cancellations

### Refund Policies

#### Cancellation-Based Refunds
- **Free Cancellation**: Full refund within policy period
- **Partial Refund**: Refund minus cancellation fees
- **No Refund**: Non-refundable bookings
- **Policy Variation**: Different policies per listing

#### Refund Processing Time
- **Credit Cards**: 5-10 business days
- **Wave Payment**: 1-3 business days
- **Bank Transfer**: 3-7 business days
- **Platform Credit**: Immediate

### Refund Process

#### Step 1: Cancellation Request
1. Go to your booking details
2. Click "Cancel Booking"
3. Review cancellation policy
4. Confirm cancellation

#### Step 2: Refund Calculation
- **Base Amount**: Original booking cost
- **Cancellation Fees**: Deducted if applicable
- **Service Fees**: May be non-refundable
- **Final Refund**: Amount to be returned

#### Step 3: Refund Processing
1. **Automatic Processing**: System processes refund
2. **Payment Method**: Refund to original payment method
3. **Confirmation**: Refund confirmation sent
4. **Tracking**: Refund status tracking available

![Refund Process](./images/refund-process-placeholder.png)
*Placeholder: Screenshot of refund processing interface*

### Dispute Resolution

#### Payment Disputes
- **Unauthorized Charges**: Report unauthorized transactions
- **Billing Errors**: Dispute incorrect charges
- **Service Issues**: Refund requests for service problems
- **Resolution Process**: Structured dispute resolution

#### Chargeback Protection
- **Documentation**: Maintain booking records
- **Communication**: Keep host/guest communication
- **Evidence**: Photos and proof of service
- **Platform Support**: Assistance with disputes

## Payment Troubleshooting

### Common Payment Issues

#### Payment Declined
**Possible Causes:**
- Insufficient funds
- Card expired or blocked
- Bank security measures
- Incorrect payment details

**Solutions:**
- Check account balance
- Verify card details
- Contact your bank
- Try alternative payment method

#### Payment Processing Errors
**Possible Causes:**
- Network connectivity issues
- Payment gateway problems
- Browser compatibility
- Security software interference

**Solutions:**
- Refresh page and retry
- Clear browser cache
- Disable ad blockers
- Try different browser

![Payment Troubleshooting](./images/payment-troubleshooting-placeholder.png)
*Placeholder: Screenshot of payment error messages and solutions*

### Getting Payment Support

#### Contact Options
- **Live Chat**: Real-time support during business hours
- **Email Support**: <EMAIL>
- **Phone Support**: Emergency payment issues
- **Help Center**: Self-service payment guides

#### Information to Provide
- **Booking Reference**: Unique booking ID
- **Payment Details**: Last 4 digits of card, payment method
- **Error Messages**: Exact error text
- **Screenshots**: Visual evidence of issues

## Payment History and Records

### Accessing Payment Records

#### Payment History Page
1. Go to Profile → "Payment History"
2. View all transactions
3. Filter by date, amount, or type
4. Download statements

#### Transaction Details
Each transaction shows:
- **Date and Time**: When payment was made
- **Booking Reference**: Associated booking
- **Amount**: Payment amount and currency
- **Payment Method**: How payment was made
- **Status**: Successful, failed, refunded

![Payment History](./images/payment-history-placeholder.png)
*Placeholder: Screenshot of payment history page*

### Tax and Business Records

#### Tax Documentation
- **Receipt Generation**: Automatic receipt creation
- **Tax Breakdown**: Detailed tax information
- **Business Expenses**: Track business travel costs
- **Annual Summaries**: Yearly spending reports

#### Export Options
- **PDF Receipts**: Individual transaction receipts
- **CSV Export**: Spreadsheet format for accounting
- **Email Reports**: Send reports to email
- **Integration**: Connect with accounting software

## International Payments

### Cross-Border Transactions

#### Currency Conversion
- **Real-Time Rates**: Current exchange rates
- **Conversion Fees**: Transparent fee structure
- **Rate Locking**: Lock rates during booking process
- **Multi-Currency**: Display prices in preferred currency

#### International Cards
- **Global Acceptance**: Visa and Mastercard worldwide
- **Foreign Transaction Fees**: Bank-imposed fees
- **Currency Selection**: Choose billing currency
- **Exchange Rate**: Bank vs. platform rates

### Regional Payment Methods

#### West African Options
- **Mobile Money**: Various mobile payment services
- **Local Banks**: Regional banking partnerships
- **Cash Alternatives**: Agent-based payment systems
- **Remittance Services**: International money transfers

![International Payments](./images/international-payments-placeholder.png)
*Placeholder: Screenshot of international payment options*

---

**Payment Best Practices:**
- Always verify payment details before submitting
- Keep payment confirmations for your records
- Check your bank statements regularly
- Report suspicious activity immediately
- Use secure internet connections for payments

**Security Tips:**
- Never share payment details via email or phone
- Log out after making payments on shared devices
- Keep your payment methods updated
- Monitor your accounts for unauthorized charges

[← Previous: Booking Management](./06-booking-management.md) | [Next: User Profile & Account Management →](./08-profile-management.md)
