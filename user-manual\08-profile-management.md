# User Profile & Account Management

This section covers managing your Gesco Stay account, including profile settings, personal information, security features, and account preferences.

## Accessing Your Profile

### Navigation to Profile

1. **Log in** to your Gesco Stay account
2. Click on your **profile picture** or **name** in the top navigation
3. Select **"Profile"** from the dropdown menu
4. Or click **"My Profile"** in the user menu

![Profile Access](./images/profile-access-placeholder.png)
*Placeholder: Screenshot showing how to access profile page*

### Profile Dashboard Overview

Your profile dashboard displays:

- **Profile Summary**: Basic information and verification status
- **Account Settings**: Privacy and notification preferences
- **Booking History**: Recent bookings and activity
- **Host Dashboard**: Hosting-related information (if applicable)
- **Payment Methods**: Saved payment options
- **Security Settings**: Password and authentication options

![Profile Dashboard](./images/profile-dashboard-placeholder.png)
*Placeholder: Screenshot of the main profile dashboard*

## Personal Information Management

### Basic Profile Information

#### Profile Picture
- **Upload Photo**: Click on profile picture to change
- **Requirements**: JPG/PNG, max 5MB, minimum 200x200 pixels
- **Guidelines**: Clear photo of yourself, professional appearance
- **Privacy**: Visible to hosts and other users you interact with

#### Personal Details
- **First Name**: Your given name
- **Last Name**: Your family name
- **Display Name**: How your name appears to others
- **Date of Birth**: For age verification (private)
- **Gender**: Optional demographic information
- **Bio**: Brief description about yourself (optional)

![Personal Information](./images/personal-info-placeholder.png)
*Placeholder: Screenshot of personal information editing form*

### Contact Information

#### Primary Contact
- **Email Address**: Primary email for communications
- **Phone Number**: Mobile number with country code
- **Verification Status**: Email and phone verification indicators
- **Communication Preferences**: How you prefer to be contacted

#### Address Information
- **Home Address**: Your primary residence (private)
- **City/Region**: Location for local recommendations
- **Country**: Your country of residence
- **Postal Code**: For location-based services

#### Emergency Contact
- **Emergency Contact Name**: Person to contact in emergencies
- **Relationship**: How they're related to you
- **Phone Number**: Emergency contact number
- **Email**: Emergency contact email (optional)

![Contact Information](./images/contact-info-placeholder.png)
*Placeholder: Screenshot of contact information section*

### Language and Regional Settings

#### Language Preferences
- **Primary Language**: Interface language
- **Secondary Languages**: Additional languages you speak
- **Translation**: Automatic translation preferences
- **Communication Language**: Preferred language for messages

#### Regional Settings
- **Time Zone**: Your local time zone
- **Currency**: Preferred currency for displaying prices
- **Date Format**: How dates are displayed
- **Number Format**: Number and decimal formatting

![Language Settings](./images/language-settings-placeholder.png)
*Placeholder: Screenshot of language and regional settings*

## Account Verification

### Verification Levels

#### Email Verification ✅
- **Status**: Verified/Unverified
- **Process**: Click verification link in email
- **Benefits**: Required for booking and hosting
- **Re-verification**: Option to resend verification email

#### Phone Verification ✅
- **Status**: Verified/Unverified
- **Process**: Enter OTP code sent via SMS
- **Benefits**: Enhanced security and communication
- **Update**: Change phone number and re-verify

#### Identity Verification ✅
- **Status**: Verified/Pending/Not Started
- **Requirements**: Government-issued ID
- **Process**: Upload ID document and selfie
- **Benefits**: Required for hosting, enhanced trust
- **Documents**: Passport, driver's license, national ID

![Verification Status](./images/verification-status-placeholder.png)
*Placeholder: Screenshot showing verification status indicators*

### Identity Verification Process

#### Step 1: Document Upload
1. Click **"Verify Identity"**
2. Select document type
3. Upload clear photo of ID
4. Ensure all text is readable

#### Step 2: Selfie Verification
1. Take a selfie holding your ID
2. Ensure face and ID are clearly visible
3. Good lighting and clear image required
4. Follow on-screen instructions

#### Step 3: Review Process
- **Processing Time**: 24-48 hours
- **Status Updates**: Email notifications
- **Additional Information**: May request more documents
- **Approval**: Verification badge added to profile

![Identity Verification](./images/identity-verification-placeholder.png)
*Placeholder: Screenshot of identity verification process*

## Security Settings

### Password Management

#### Changing Password
1. Go to **Security Settings**
2. Click **"Change Password"**
3. Enter current password
4. Create new strong password
5. Confirm new password

#### Password Requirements
- **Minimum Length**: 8 characters
- **Complexity**: Mix of letters, numbers, symbols
- **Uniqueness**: Different from previous passwords
- **Strength Indicator**: Real-time password strength meter

![Password Settings](./images/password-settings-placeholder.png)
*Placeholder: Screenshot of password change interface*

### Two-Factor Authentication (2FA)

#### Setting Up 2FA
1. Go to **Security Settings**
2. Click **"Enable Two-Factor Authentication"**
3. Choose authentication method:
   - **SMS**: Text message codes
   - **Authenticator App**: Google Authenticator, Authy
   - **Email**: Email-based codes

#### 2FA Process
1. **Login**: Enter username and password
2. **Second Factor**: Enter code from chosen method
3. **Verification**: Access granted after successful verification
4. **Backup Codes**: Save backup codes for emergencies

#### Managing 2FA
- **Disable 2FA**: Turn off two-factor authentication
- **Change Method**: Switch between SMS, app, or email
- **Backup Codes**: Generate new backup codes
- **Trusted Devices**: Manage remembered devices

![Two-Factor Authentication](./images/2fa-settings-placeholder.png)
*Placeholder: Screenshot of 2FA setup interface*

### Login Activity

#### Recent Activity
- **Login History**: Recent login attempts
- **Device Information**: Browser, operating system, location
- **IP Addresses**: Login locations
- **Suspicious Activity**: Flagged unusual logins

#### Security Alerts
- **New Device**: Notifications for new device logins
- **Location Changes**: Alerts for logins from new locations
- **Failed Attempts**: Notifications of failed login attempts
- **Password Changes**: Alerts for password modifications

![Login Activity](./images/login-activity-placeholder.png)
*Placeholder: Screenshot of login activity log*

## Privacy Settings

### Profile Visibility

#### Public Information
- **Profile Picture**: Visible to all users
- **Display Name**: Shown in listings and messages
- **Bio**: Public description (optional)
- **Verification Badges**: Trust indicators
- **Reviews**: Public reviews and ratings

#### Private Information
- **Full Name**: Only visible to confirmed bookings
- **Contact Details**: Shared only when necessary
- **Address**: Never shared publicly
- **Date of Birth**: Private demographic data

#### Visibility Controls
- **Search Visibility**: Appear in user searches
- **Profile Indexing**: Allow search engines to index
- **Social Sharing**: Allow profile sharing on social media
- **Contact Preferences**: How others can contact you

![Privacy Settings](./images/privacy-settings-placeholder.png)
*Placeholder: Screenshot of privacy control settings*

### Data Management

#### Data Download
- **Personal Data**: Download all your data
- **Booking History**: Export booking records
- **Messages**: Download message history
- **Reviews**: Export reviews given and received

#### Data Deletion
- **Account Deletion**: Permanently delete account
- **Data Retention**: What data is kept after deletion
- **Cooling Period**: Time to reactivate deleted account
- **Irreversible**: Permanent deletion after cooling period

#### Data Sharing
- **Third Parties**: Control data sharing with partners
- **Marketing**: Opt-out of marketing communications
- **Analytics**: Anonymous usage data sharing
- **Personalization**: Data use for personalized experiences

## Notification Preferences

### Communication Settings

#### Email Notifications
- **Booking Confirmations**: Reservation confirmations
- **Payment Receipts**: Payment and refund notifications
- **Host Messages**: Messages from property owners
- **Platform Updates**: Important platform announcements
- **Marketing**: Promotional emails and offers
- **Security Alerts**: Account security notifications

#### SMS Notifications
- **Booking Reminders**: Check-in reminders
- **Emergency Alerts**: Urgent safety notifications
- **Verification Codes**: OTP and security codes
- **Payment Alerts**: Payment confirmations
- **Host Communications**: Urgent messages from hosts

#### Push Notifications (Mobile App)
- **Real-time Messages**: Instant message notifications
- **Booking Updates**: Status changes and confirmations
- **Special Offers**: Limited-time deals and promotions
- **Location-based**: Nearby recommendations and offers

![Notification Settings](./images/notification-settings-placeholder.png)
*Placeholder: Screenshot of notification preferences*

### Frequency Controls

#### Notification Frequency
- **Immediate**: Real-time notifications
- **Daily Digest**: Once daily summary
- **Weekly Summary**: Weekly activity summary
- **Monthly**: Monthly updates only
- **Off**: Disable specific notification types

#### Quiet Hours
- **Do Not Disturb**: Set quiet hours for notifications
- **Time Zone**: Respect your local time zone
- **Emergency Override**: Allow urgent notifications
- **Weekend Settings**: Different settings for weekends

## Payment Methods

### Saved Payment Methods

#### Credit/Debit Cards
- **Card Management**: Add, edit, or remove cards
- **Default Card**: Set primary payment method
- **Expiration Alerts**: Notifications for expiring cards
- **Security**: Last 4 digits displayed only

#### Mobile Money
- **Wave Account**: Link Wave mobile money account
- **Verification**: Verify mobile money account
- **Transaction Limits**: View account limits
- **Balance**: Check available balance

#### Bank Accounts
- **Account Linking**: Connect bank accounts
- **Verification**: Micro-deposit verification
- **Direct Debit**: Authorize automatic payments
- **Security**: Bank-grade encryption

![Payment Methods](./images/payment-methods-placeholder.png)
*Placeholder: Screenshot of saved payment methods*

### Payment Preferences

#### Default Settings
- **Primary Method**: Default payment option
- **Currency**: Preferred billing currency
- **Auto-pay**: Automatic payment for bookings
- **Receipts**: Email receipt preferences

#### Security Features
- **Payment Verification**: Require verification for payments
- **Spending Limits**: Set monthly spending limits
- **Fraud Alerts**: Notifications for suspicious activity
- **Payment History**: Access to all transaction records

## Host Profile (If Applicable)

### Host Information

#### Host Status
- **Host Level**: New, Experienced, Superhost
- **Hosting Since**: When you started hosting
- **Response Rate**: How quickly you respond to inquiries
- **Response Time**: Average response time
- **Acceptance Rate**: Percentage of bookings accepted

#### Host Verification
- **Identity Verified**: Government ID verification
- **Phone Verified**: Phone number verification
- **Email Verified**: Email address verification
- **Property Verified**: Property ownership verification

![Host Profile](./images/host-profile-placeholder.png)
*Placeholder: Screenshot of host profile section*

### Host Preferences

#### Booking Settings
- **Instant Book**: Allow immediate bookings
- **Advance Notice**: Minimum booking notice required
- **Trip Length**: Minimum and maximum stay duration
- **Guest Requirements**: Requirements for guests

#### Communication
- **Preferred Contact**: Phone, email, or platform messaging
- **Language**: Languages you can communicate in
- **Availability**: When you're available to respond
- **Auto-responses**: Automated message templates

## Account Deletion and Deactivation

### Temporary Deactivation

#### Deactivation Process
1. Go to **Account Settings**
2. Click **"Deactivate Account"**
3. Select reason for deactivation
4. Confirm deactivation

#### During Deactivation
- **Profile Hidden**: Profile not visible to others
- **Bookings**: Existing bookings remain active
- **Messages**: Can still receive important messages
- **Reactivation**: Can reactivate anytime

### Permanent Deletion

#### Before Deletion
- **Complete Bookings**: Finish all active bookings
- **Resolve Issues**: Address any outstanding problems
- **Download Data**: Save any important information
- **Cancel Subscriptions**: End any recurring payments

#### Deletion Process
1. **Request Deletion**: Submit deletion request
2. **Verification**: Confirm identity and intent
3. **Cooling Period**: 30-day period to change mind
4. **Final Deletion**: Permanent removal after cooling period

#### What Gets Deleted
- **Personal Information**: All personal data removed
- **Booking History**: Booking records deleted
- **Messages**: All message history removed
- **Reviews**: Reviews given and received removed

![Account Deletion](./images/account-deletion-placeholder.png)
*Placeholder: Screenshot of account deletion options*

---

**Profile Management Tips:**
- Keep your information updated for better service
- Use a clear, professional profile picture
- Complete verification for enhanced trust
- Review privacy settings regularly
- Enable two-factor authentication for security

**Security Best Practices:**
- Use a strong, unique password
- Enable two-factor authentication
- Monitor login activity regularly
- Keep contact information current
- Report suspicious activity immediately

[← Previous: Payment System](./07-payment-system.md) | [Next: Host Features →](./09-host-features.md)
