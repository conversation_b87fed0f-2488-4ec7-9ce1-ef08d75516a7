# User Manual Images

This directory contains placeholder images for the Gesco Stay user manual. These placeholders should be replaced with actual screenshots of the platform.

## Image Requirements

### General Guidelines
- **Format**: PNG or JPG
- **Resolution**: Minimum 1920x1080 for desktop screenshots
- **Mobile**: 375x812 for mobile screenshots
- **Quality**: High quality, clear text
- **Compression**: Optimized for web viewing

### Screenshot Standards
- **Clean Interface**: No personal information visible
- **Consistent Styling**: Use same browser/device
- **Annotations**: Add callouts and labels where helpful
- **Cropping**: Focus on relevant interface elements

## Placeholder Images Needed

### Getting Started (01-getting-started.md)
- `platform-overview-placeholder.png` - Homepage showing all services
- `signup-button-placeholder.png` - Sign up button location
- `registration-method-placeholder.png` - Email/phone selection
- `registration-form-placeholder.png` - Complete registration form
- `otp-verification-placeholder.png` - OTP verification page
- `email-login-placeholder.png` - Email login form
- `phone-login-placeholder.png` - Phone login form
- `profile-setup-placeholder.png` - Profile setup page

### Navigation & Interface (02-navigation-interface.md)
- `homepage-header-placeholder.png` - Main header with navigation
- `hero-section-placeholder.png` - Hero section with search
- `categories-section-placeholder.png` - Service categories
- `navigation-menu-placeholder.png` - Expanded navigation menu
- `mobile-navigation-placeholder.png` - Mobile hamburger menu
- `property-search-placeholder.png` - Property search interface
- `search-filters-placeholder.png` - Advanced search filters
- `car-search-placeholder.png` - Car search interface
- `listing-cards-placeholder.png` - Property/car listing cards
- `mobile-interface-placeholder.png` - Mobile responsive design

### Property Listings (03-property-listings.md)
- `properties-page-placeholder.png` - Main properties listing page
- `property-search-form-placeholder.png` - Property search form
- `property-filters-placeholder.png` - Advanced filter options
- `property-cards-placeholder.png` - Multiple property cards
- `property-gallery-placeholder.png` - Property image gallery
- `property-description-placeholder.png` - Property description section
- `property-map-placeholder.png` - Property location map
- `property-reviews-placeholder.png` - Reviews and ratings
- `booking-widget-placeholder.png` - Property booking widget
- `create-listing-basics-placeholder.png` - Basic listing info form
- `location-setup-placeholder.png` - Location and map setup
- `amenities-selection-placeholder.png` - Amenities selection
- `photo-upload-placeholder.png` - Photo upload interface
- `pricing-setup-placeholder.png` - Pricing configuration
- `listing-review-placeholder.png` - Final listing review

### Car Rentals (04-car-rentals.md)
- `car-listings-page-placeholder.png` - Main car listings page
- `car-search-form-placeholder.png` - Car search form
- `car-filters-placeholder.png` - Car search filters
- `car-cards-placeholder.png` - Multiple car listing cards
- `car-gallery-placeholder.png` - Car image gallery
- `car-features-placeholder.png` - Car features and equipment
- `pickup-location-placeholder.png` - Pickup location map
- `car-booking-widget-placeholder.png` - Car booking widget
- `car-insurance-placeholder.png` - Insurance selection
- `car-booking-confirmation-placeholder.png` - Booking confirmation
- `car-basic-info-placeholder.png` - Basic car info form
- `car-features-selection-placeholder.png` - Car features selection
- `car-photo-upload-placeholder.png` - Car photo upload
- `car-availability-placeholder.png` - Availability calendar
- `car-pricing-placeholder.png` - Car pricing setup
- `car-listing-review-placeholder.png` - Final car listing review

### Hotel Bookings (05-hotel-bookings.md)
- `hotel-listings-page-placeholder.png` - Main hotel listings page
- `hotel-search-form-placeholder.png` - Hotel search form
- `hotel-filters-placeholder.png` - Hotel search filters
- `hotel-cards-placeholder.png` - Multiple hotel cards
- `hotel-gallery-placeholder.png` - Hotel image gallery
- `hotel-room-types-placeholder.png` - Available room types
- `hotel-amenities-placeholder.png` - Hotel amenities list
- `hotel-location-placeholder.png` - Hotel location map
- `hotel-reviews-placeholder.png` - Hotel reviews section
- `hotel-booking-widget-placeholder.png` - Hotel booking widget
- `hotel-addons-placeholder.png` - Hotel add-on services
- `hotel-booking-confirmation-placeholder.png` - Booking confirmation
- `hotel-basic-info-placeholder.png` - Basic hotel info form
- `hotel-room-setup-placeholder.png` - Room type configuration
- `hotel-facilities-placeholder.png` - Hotel facilities setup
- `hotel-photo-upload-placeholder.png` - Hotel photo upload
- `hotel-pricing-placeholder.png` - Hotel pricing setup
- `hotel-listing-review-placeholder.png` - Final hotel listing review

### Booking Management (06-booking-management.md)
- `bookings-navigation-placeholder.png` - How to access bookings
- `bookings-dashboard-placeholder.png` - Main bookings dashboard
- `booking-cards-placeholder.png` - Different booking cards
- `detailed-booking-placeholder.png` - Detailed booking view
- `booking-status-placeholder.png` - Different booking statuses
- `property-modification-placeholder.png` - Property booking modification
- `car-modification-placeholder.png` - Car booking modification
- `cancellation-policies-placeholder.png` - Cancellation policies
- `cancellation-process-placeholder.png` - Cancellation confirmation
- `messaging-system-placeholder.png` - Messaging interface
- `review-interface-placeholder.png` - Review submission form
- `booking-history-placeholder.png` - Booking history with filters

### Payment System (07-payment-system.md)
- `credit-card-payment-placeholder.png` - Credit card payment interface
- `wave-payment-placeholder.png` - Wave payment interface
- `currency-selection-placeholder.png` - Currency selection
- `payment-summary-placeholder.png` - Payment summary page
- `payment-confirmation-placeholder.png` - Payment confirmation
- `payment-security-placeholder.png` - Security features highlight
- `payment-holds-placeholder.png` - Payment holds explanation
- `fee-breakdown-placeholder.png` - Detailed fee breakdown
- `refund-process-placeholder.png` - Refund processing interface
- `payment-troubleshooting-placeholder.png` - Payment error messages
- `payment-history-placeholder.png` - Payment history page
- `international-payments-placeholder.png` - International payment options

## Image Creation Guidelines

### Desktop Screenshots
1. Use Chrome or Safari browser
2. Set browser window to 1920x1080
3. Hide browser bookmarks bar
4. Use incognito/private mode for clean interface
5. Ensure good lighting and contrast

### Mobile Screenshots
1. Use iPhone 12/13 or equivalent Android device
2. Portrait orientation (375x812)
3. Hide status bar if possible
4. Ensure touch targets are visible
5. Show keyboard interactions where relevant

### Annotation Guidelines
1. Use consistent arrow styles and colors
2. Add numbered callouts for step-by-step processes
3. Highlight important buttons and links
4. Use red boxes for critical information
5. Keep annotations minimal and clear

### File Naming Convention
- Use descriptive names matching the placeholder references
- Include section number prefix (e.g., `01-`, `02-`)
- Use lowercase with hyphens
- Include device type for mobile screenshots (e.g., `-mobile`)

## Replacement Process

1. Take actual screenshots of the Gesco Stay platform
2. Edit and annotate as needed
3. Optimize file sizes for web
4. Replace placeholder files with same names
5. Update alt text in markdown files if needed
6. Test all image links in the manual

---

**Note**: All placeholder images should be replaced with actual screenshots before publishing the user manual.
