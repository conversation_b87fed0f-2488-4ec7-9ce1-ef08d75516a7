<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gesco Stay Web Platform - User Manual</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        /* Additional styles for the index page */
        .section-preview {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #8B4513;
        }
        
        .section-preview h3 {
            margin-top: 0;
            color: #8B4513;
        }
        
        .section-preview p {
            color: #666;
            margin-bottom: 15px;
        }
        
        .section-preview a {
            display: inline-block;
            background: #8B4513;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            transition: background 0.2s ease;
        }
        
        .section-preview a:hover {
            background: #A0522D;
            border-bottom: none;
        }
        
        .manual-info {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .manual-info h2 {
            color: #2e7d32;
            margin-top: 0;
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .quick-link {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            transition: transform 0.2s ease;
        }
        
        .quick-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .quick-link a {
            font-weight: 600;
            color: #8B4513;
        }
    </style>
</head>
<body>
    <div class="manual-wrapper">
        <header class="manual-header">
            <div class="container">
                <h1>Gesco Stay Web Platform</h1>
                <p>Complete User Manual & Guide</p>
            </div>
        </header>

        <div class="manual-content">
            <div class="container">
                <div class="manual-info">
                    <h2>📖 About This Manual</h2>
                    <p>Welcome to the comprehensive user manual for the Gesco Stay web platform. This manual covers all features and functionalities of our travel and accommodation platform, designed to help both new and experienced users navigate the system effectively.</p>
                    <p><strong>Version:</strong> 1.0 | <strong>Last Updated:</strong> August 2025 | <strong>Platform:</strong> Web Application</p>
                </div>

                <div class="quick-links">
                    <div class="quick-link">
                        <h4>🚀 New Users</h4>
                        <p>Start here if you're new to Gesco Stay</p>
                        <a href="01-getting-started.md">Getting Started Guide</a>
                    </div>
                    <div class="quick-link">
                        <h4>🏠 Property Hosts</h4>
                        <p>Learn how to list and manage properties</p>
                        <a href="09-host-features.md">Host Features</a>
                    </div>
                    <div class="quick-link">
                        <h4>💳 Payment Help</h4>
                        <p>Payment methods and troubleshooting</p>
                        <a href="07-payment-system.md">Payment System</a>
                    </div>
                    <div class="quick-link">
                        <h4>🆘 Need Help?</h4>
                        <p>Troubleshooting and support</p>
                        <a href="12-troubleshooting-support.md">Get Support</a>
                    </div>
                </div>

                <h2>📚 Manual Sections</h2>

                <div class="section-preview">
                    <h3>1. Getting Started</h3>
                    <p>Learn the basics of using Gesco Stay, including account creation, verification, and initial setup.</p>
                    <a href="01-getting-started.md">Read Section →</a>
                </div>

                <div class="section-preview">
                    <h3>2. Navigation & Interface</h3>
                    <p>Understand the platform's user interface, navigation menu, and search functionality.</p>
                    <a href="02-navigation-interface.md">Read Section →</a>
                </div>

                <div class="section-preview">
                    <h3>3. Property Listings</h3>
                    <p>Browse properties, understand booking process, and learn how to create property listings.</p>
                    <a href="03-property-listings.md">Read Section →</a>
                </div>

                <div class="section-preview">
                    <h3>4. Car Rentals</h3>
                    <p>Discover car rental options, booking process, and how to list your vehicle for rental.</p>
                    <a href="04-car-rentals.md">Read Section →</a>
                </div>

                <div class="section-preview">
                    <h3>5. Hotel Bookings</h3>
                    <p>Search and book hotels, understand hotel features, and learn about hotel listing creation.</p>
                    <a href="05-hotel-bookings.md">Read Section →</a>
                </div>

                <div class="section-preview">
                    <h3>6. Booking Management</h3>
                    <p>View, modify, and manage all your bookings including cancellations and communication.</p>
                    <a href="06-booking-management.md">Read Section →</a>
                </div>

                <div class="section-preview">
                    <h3>7. Payment System</h3>
                    <p>Understand payment methods, security features, and refund processes.</p>
                    <a href="07-payment-system.md">Read Section →</a>
                </div>

                <div class="section-preview">
                    <h3>8. User Profile & Account Management</h3>
                    <p>Manage your profile, security settings, and account preferences.</p>
                    <a href="08-profile-management.md">Read Section →</a>
                </div>

                <div class="section-preview">
                    <h3>9. Host Features</h3>
                    <p>Comprehensive guide for hosts including property management and guest communication.</p>
                    <a href="09-host-features.md">Read Section →</a>
                </div>

                <div class="section-preview">
                    <h3>10. Admin Features</h3>
                    <p>Administrative tools and features for platform administrators (admin access required).</p>
                    <a href="10-admin-features.md">Read Section →</a>
                </div>

                <div class="section-preview">
                    <h3>11. Legal & Policies</h3>
                    <p>Terms of service, privacy policy, safety guidelines, and community standards.</p>
                    <a href="11-legal-policies.md">Read Section →</a>
                </div>

                <div class="section-preview">
                    <h3>12. Troubleshooting & Support</h3>
                    <p>Common issues, solutions, FAQ, and how to get help when you need it.</p>
                    <a href="12-troubleshooting-support.md">Read Section →</a>
                </div>

                <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                    <h3>📞 Need Additional Help?</h3>
                    <p>If you need assistance beyond what's covered in this manual:</p>
                    <ul>
                        <li><strong>Email:</strong> <EMAIL></li>
                        <li><strong>Live Chat:</strong> Available on the platform during business hours</li>
                        <li><strong>Emergency Support:</strong> 24/7 for safety-related issues</li>
                        <li><strong>Help Center:</strong> Self-service support articles on the platform</li>
                    </ul>
                </div>

                <div style="margin-top: 30px; padding: 20px; background: #fff3e0; border-radius: 6px; border-left: 4px solid #ff9800;">
                    <h3>⚠️ Important Notes</h3>
                    <ul>
                        <li>This manual covers the web platform. Mobile app instructions may differ.</li>
                        <li>Screenshots are placeholders and should be replaced with actual platform images.</li>
                        <li>Features and interfaces may change with platform updates.</li>
                        <li>Always refer to the latest version of this manual for current information.</li>
                    </ul>
                </div>
            </div>
        </div>

        <footer class="manual-footer">
            <div class="container">
                <p>&copy; 2025 Gesco Stay. All rights reserved.</p>
                <p>This user manual is designed to help you make the most of the Gesco Stay platform.</p>
                <p>For the latest updates and information, visit <a href="https://gescostay.com">gescostay.com</a></p>
            </div>
        </footer>
    </div>

    <script>
        // Simple script to handle smooth scrolling and enhance user experience
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling to all links
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add hover effects to section previews
            const sectionPreviews = document.querySelectorAll('.section-preview');
            sectionPreviews.forEach(preview => {
                preview.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)';
                });
                
                preview.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                });
            });

            // Add a "back to top" button functionality
            const backToTopButton = document.createElement('button');
            backToTopButton.innerHTML = '↑ Top';
            backToTopButton.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: #8B4513;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 6px;
                cursor: pointer;
                display: none;
                z-index: 1000;
                transition: opacity 0.3s ease;
            `;
            
            document.body.appendChild(backToTopButton);

            // Show/hide back to top button based on scroll position
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.style.display = 'block';
                } else {
                    backToTopButton.style.display = 'none';
                }
            });

            // Scroll to top when button is clicked
            backToTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
