/* Gesco Stay User Manual Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    font-size: 16px;
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.manual-wrapper {
    background: white;
    margin: 20px auto;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Header Styles */
.manual-header {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    color: white;
    padding: 40px 20px;
    text-align: center;
}

.manual-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.manual-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Navigation Styles */
.manual-nav {
    background: #f1f3f4;
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.nav-item {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #8B4513;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.nav-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-item a {
    text-decoration: none;
    color: #333;
    font-weight: 600;
}

.nav-item p {
    margin-top: 5px;
    font-size: 0.9rem;
    color: #666;
}

/* Content Styles */
.manual-content {
    padding: 40px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: #2c3e50;
    margin-bottom: 20px;
    line-height: 1.3;
}

h1 {
    font-size: 2.5rem;
    border-bottom: 3px solid #8B4513;
    padding-bottom: 15px;
    margin-bottom: 30px;
}

h2 {
    font-size: 2rem;
    color: #8B4513;
    margin-top: 40px;
    margin-bottom: 20px;
}

h3 {
    font-size: 1.5rem;
    color: #A0522D;
    margin-top: 30px;
    margin-bottom: 15px;
}

h4 {
    font-size: 1.25rem;
    color: #5D4037;
    margin-top: 25px;
    margin-bottom: 12px;
}

h5 {
    font-size: 1.1rem;
    color: #6D4C41;
    margin-top: 20px;
    margin-bottom: 10px;
}

/* Paragraph and Text */
p {
    margin-bottom: 16px;
    text-align: justify;
}

strong {
    color: #2c3e50;
    font-weight: 600;
}

em {
    color: #5D4037;
    font-style: italic;
}

/* Lists */
ul, ol {
    margin-bottom: 20px;
    padding-left: 30px;
}

li {
    margin-bottom: 8px;
}

ul li {
    list-style-type: disc;
}

ol li {
    list-style-type: decimal;
}

/* Nested lists */
ul ul, ol ol, ul ol, ol ul {
    margin-top: 8px;
    margin-bottom: 8px;
}

/* Links */
a {
    color: #8B4513;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-bottom 0.2s ease;
}

a:hover {
    border-bottom: 1px solid #8B4513;
}

/* Images and Placeholders */
img {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
}

.image-placeholder {
    background: #f0f0f0;
    border: 2px dashed #ccc;
    border-radius: 6px;
    padding: 40px;
    text-align: center;
    margin: 20px 0;
    color: #666;
}

.image-placeholder::before {
    content: "📷 ";
    font-size: 2rem;
    display: block;
    margin-bottom: 10px;
}

/* Code and Technical Elements */
code {
    background: #f4f4f4;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #d63384;
}

pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    overflow-x: auto;
    margin: 20px 0;
}

pre code {
    background: none;
    padding: 0;
    color: #333;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

th {
    background: #8B4513;
    color: white;
    font-weight: 600;
}

tr:hover {
    background: #f8f9fa;
}

/* Blockquotes and Callouts */
blockquote {
    border-left: 4px solid #8B4513;
    padding: 20px;
    margin: 20px 0;
    background: #f8f9fa;
    border-radius: 0 6px 6px 0;
    font-style: italic;
}

.note {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
    padding: 15px;
    margin: 20px 0;
    border-radius: 0 6px 6px 0;
}

.warning {
    background: #fff3e0;
    border-left: 4px solid #ff9800;
    padding: 15px;
    margin: 20px 0;
    border-radius: 0 6px 6px 0;
}

.danger {
    background: #ffebee;
    border-left: 4px solid #f44336;
    padding: 15px;
    margin: 20px 0;
    border-radius: 0 6px 6px 0;
}

.success {
    background: #e8f5e8;
    border-left: 4px solid #4caf50;
    padding: 15px;
    margin: 20px 0;
    border-radius: 0 6px 6px 0;
}

/* Step-by-step Instructions */
.steps {
    counter-reset: step-counter;
}

.step {
    counter-increment: step-counter;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    margin: 15px 0;
    position: relative;
    padding-left: 60px;
}

.step::before {
    content: counter(step-counter);
    position: absolute;
    left: 20px;
    top: 20px;
    background: #8B4513;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

/* Navigation Between Pages */
.page-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    background: #8B4513;
    color: white;
    border-radius: 6px;
    text-decoration: none;
    transition: background 0.2s ease;
}

.nav-link:hover {
    background: #A0522D;
    border-bottom: none;
}

.nav-link.prev::before {
    content: "← ";
    margin-right: 5px;
}

.nav-link.next::after {
    content: " →";
    margin-left: 5px;
}

/* Footer */
.manual-footer {
    background: #2c3e50;
    color: white;
    padding: 30px 20px;
    text-align: center;
}

.manual-footer p {
    margin-bottom: 10px;
}

.manual-footer a {
    color: #8B4513;
}

/* Responsive Design */
@media (max-width: 768px) {
    .manual-content {
        padding: 20px;
    }
    
    .manual-header h1 {
        font-size: 2rem;
    }
    
    .manual-header p {
        font-size: 1rem;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    h3 {
        font-size: 1.25rem;
    }
    
    .nav-grid {
        grid-template-columns: 1fr;
    }
    
    .page-navigation {
        flex-direction: column;
        gap: 10px;
    }
    
    .step {
        padding-left: 20px;
    }
    
    .step::before {
        position: static;
        margin-bottom: 10px;
    }
}

/* Print Styles */
@media print {
    body {
        background: white;
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .manual-wrapper {
        box-shadow: none;
        margin: 0;
    }
    
    .manual-header {
        background: none;
        color: black;
        border-bottom: 2px solid black;
    }
    
    .manual-nav {
        display: none;
    }
    
    .page-navigation {
        display: none;
    }
    
    a {
        color: black;
        text-decoration: underline;
    }
    
    .image-placeholder {
        border: 1px solid #ccc;
        background: #f9f9f9;
    }
}
